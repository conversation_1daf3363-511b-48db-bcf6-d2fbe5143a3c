// Fichier : src/renderer/src/components/MainLayout.jsx
import React from 'react';
import { Link, useLocation } from 'react-router-dom';

function MainLayout({ children }) {
    const location = useLocation();

    // Tu copieras ici le HTML de ta barre de navigation depuis ton ancien index.html
    const navLinks = [
        { path: '/', label: 'Tableau de bord' },
        { path: '/products', label: 'Produits' },
        { path: '/clients', label: 'Clients' },
        { path: '/history', label: 'Historique' },
        { path: '/settings', label: 'Paramètres' },
    ];

    return (
        <div style={{ display: 'flex', height: '100vh' }}>
            <aside style={{ width: '250px', background: '#f4f4f4', padding: '1rem' }}>
                <h2>GestionPro</h2>
                <nav>
                    <ul>
                        {navLinks.map(link => (
                            <li key={link.path} style={{
                                listStyle: 'none',
                                margin: '1rem 0',
                                background: location.pathname === link.path ? '#ddd' : 'transparent'
                            }}>
                                <Link to={link.path} style={{ textDecoration: 'none', color: 'black' }}>
                                    {link.label}
                                </Link>
                            </li>
                        ))}
                    </ul>
                </nav>
            </aside>
            <main style={{ flex: 1, padding: '2rem', overflowY: 'auto' }}>
                {children}
            </main>
        </div>
    );
}

export default MainLayout;

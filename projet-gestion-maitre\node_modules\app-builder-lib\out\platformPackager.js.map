{"version": 3, "file": "platformPackager.js", "sourceRoot": "", "sources": ["../src/platformPackager.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAA8J;AAC9J,gDAAkF;AAClF,4CAAiE;AACjE,sDAA2D;AAC3D,0CAAqC;AACrC,uCAA+B;AAE/B,6BAA4B;AAC5B,6BAAmC;AACnC,uCAAmC;AACnC,4DAA2D;AAC3D,8CAA8C;AAC9C,gDAA8C;AAC9C,+CAA8I;AAC9I,uDAA4E;AAC5E,2CAAwD;AACxD,mCAagB;AAChB,kDAA2D;AAC3D,wDAA+I;AAC/I,wDAAmE;AAEnE,MAAsB,gBAAgB;IACpC,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAA;IAC1B,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAA;IACpC,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAA;IAC7B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;IACzB,CAAC;IAID,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAA;IACjC,CAAC;IAMD,YACW,IAAc,EACd,QAAkB;QADlB,SAAI,GAAJ,IAAI,CAAU;QACd,aAAQ,GAAR,QAAQ,CAAU;QANZ,kBAAa,GAAG,IAAI,eAAI,CAAgB,GAAG,EAAE,CAAC,IAAA,0BAAgB,EAAC,IAAA,kBAAO,EAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QAQxH,IAAI,CAAC,4BAA4B,GAAG,gBAAgB,CAAC,qCAAqC,CAAE,IAAI,CAAC,MAAc,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAA;QAChJ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;IAClD,CAAC;IAED,IAAI,WAAW;QACb,MAAM,WAAW,GAAG,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAA;QACjE,0FAA0F;QAC1F,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO,QAAQ,CAAA;QACjB,CAAC;QACD,OAAO,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,QAAQ,CAAA;IAC3D,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAA;IAC9B,CAAC;IAID,2BAA2B;IACjB,cAAc,CAAC,OAAgB;QACvC,OAAO,IAAI,iBAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,CAAA;IACxE,CAAC;IAEO,MAAM,CAAC,qCAAqC,CAAC,OAA+B;QAClF,OAAO,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IACxD,CAAC;IAIS,cAAc;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACxC,IAAI,IAAA,8BAAe,EAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,iCAAiC,EAAE,EAAE,8CAA8C,CAAC,CAAA;YACvG,OAAO,EAAE,CAAA;QACX,CAAC;aAAM,CAAC;YACN,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAA;QACxB,CAAC;IACH,CAAC;IAES,UAAU,CAAC,YAA4B;QAC/C,mCAAmC;QACnC,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC7G,OAAO,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,4BAA4B,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,CAAA;IACpH,CAAC;IAES,gBAAgB;QACxB,mCAAmC;QACnC,OAAO,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,IAAI,CAAC,4BAA4B,CAAC,cAAc,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAA;IACtJ,CAAC;IAES,gBAAgB,CAAC,MAAc,EAAE,IAAU;QACnD,OAAO,CACL,IAAI,CAAC,eAAe,CAAC,WAAW;YAChC,IAAI,CAAC,IAAI,CACP,MAAM,EACN,GAAG,IAAI,CAAC,QAAQ,CAAC,qBAAqB,GAAG,IAAA,4BAAa,EAAC,IAAI,EAAE,IAAI,CAAC,4BAA4B,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAClK,CACF,CAAA;IACH,CAAC;IAED,uBAAuB,CAAC,IAAY,EAAE,MAAqB,EAAE,IAAiB,EAAE,gBAAgC;QAC9G,OAAO,IAAI,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC1C,IAAI;YACJ,gBAAgB;YAChB,MAAM;YACN,IAAI;YACJ,QAAQ,EAAE,IAAI;SACf,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,IAAU,EAAE,OAAsB,EAAE,WAA6B;QAC1F,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACrD,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAgC,EAAE,IAAI,EAAE,IAAI,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAA;QACtI,IAAI,CAAC,4BAA4B,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC,CAAA;IAC1E,CAAC;IAES,4BAA4B,CAAC,SAAiB,EAAE,IAAU,EAAE,OAAsB,EAAE,WAA6B;QACzH,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,IAAI,EAAE,CAAC;YACrD,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;YACzE,OAAM;QACR,CAAC;QAED,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YACzB,6IAA6I;YAC7I,MAAM,cAAc,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YACxE,gBAAgB,CAAC,iBAAiB,CAAC,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;YAC5E,MAAM,cAAc,CAAC,UAAU,EAAE,CAAA;YAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBAC7B,MAAM,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;gBACrC,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAEO,MAAM,CAAC,iBAAiB,CAAC,OAAsB,EAAE,WAA6B,EAAE,SAAiB,EAAE,IAAU;QACnH,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;gBAC5B,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;YACpD,CAAC;QACH,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,WAAoB,EAAE,SAAiB,EAAE,OAA+B;QACnG,MAAM,IAAI,GAAG,WAAW;YACtB,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YACjC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG;gBAC9B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,EAAE,UAAU,CAAC;gBACzE,CAAC,CAAC,SAAS,CAAA;QACf,OAAO,IAAA,6BAAe,EAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACnG,CAAC;IAED,4BAA4B,CAAC,MAAc,EAAE,IAAU,EAAE,kBAAgD;QACvG,OAAO;YACL,aAAa,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC;YACjG,kBAAkB;YAClB,YAAY,EAAE,MAAM;YACpB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC5B,CAAA;IACH,CAAC;IAES,KAAK,CAAC,MAAM,CACpB,MAAc,EACd,SAAiB,EACjB,YAAkC,EAClC,IAAU,EACV,4BAAgC,EAChC,OAAsB,EACtB,IAAI,GAAG,IAAI,EACX,oBAAoB,GAAG,KAAK;QAE5B,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7C,OAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,+KAA+K;QAC/K,OAAO,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAA;QAEnC,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,CAAA;QACjG,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,UAAU,CAAC;gBACf,SAAS;gBACT,MAAM;gBACN,IAAI;gBACJ,OAAO;gBACP,QAAQ,EAAE,IAAI;gBACd,oBAAoB,EAAE,YAAY;aACnC,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,kBAAG,CAAC,IAAI,CACN;YACE,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;YAChB,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,OAAO;YACxC,SAAS,EAAE,kBAAG,CAAC,QAAQ,CAAC,SAAS,CAAC;SACnC,EACD,WAAW,CACZ,CAAA;QAED,MAAM,SAAS,CAAC,gCAAgC,CAAC;YAC/C,QAAQ,EAAE,IAAI;YACd,SAAS;YACT,YAAY;YACZ,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;YAChB,OAAO,EAAE,SAAS,CAAC,OAAO;SAC3B,CAAC,CAAA;QAEF,MAAM,eAAe,GAAqB,EAAE,CAAA;QAE5C,MAAM,qBAAqB,GAAG,CAAC,QAAmC,EAAE,EAAE;YACpE,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,OAAO,CAAC,qBAAqB,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBACtE,CAAC;YACH,CAAC;QACH,CAAC,CAAA;QAED,MAAM,sBAAsB,GAAG,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,IAAI,EAAE,4BAA4B,CAAC,CAAA;QAC5G,MAAM,aAAa,GAAG,sBAAsB,CAAC,aAAa,CAAA;QAC1D,MAAM,qBAAqB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAA;QAChG,qBAAqB,CAAC,qBAAqB,CAAC,CAAA;QAC5C,MAAM,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,sBAAsB,CAAC,CAAA;QAC7F,qBAAqB,CAAC,iBAAiB,CAAC,CAAA;QAExC,MAAM,WAAW,GAAqB;YACpC,SAAS;YACT,MAAM;YACN,IAAI;YACJ,OAAO;YACP,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,YAAY;SACnC,CAAA;QAED,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAA;QAC/E,MAAM,aAAa,GACjB,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG;YAC5B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,gBAAgB,EAAE,UAAU,EAAE,WAAW,CAAC;YAC3E,CAAC,CAAC,IAAA,2BAAe,EAAC,SAAS,CAAC;gBAC1B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC;gBACnC,CAAC,CAAC,SAAS,CAAA;QACjB,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QACrE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE,WAAW,EAAE,4BAA4B,EAAE,eAAe,EAAE,aAAa,CAAC,CAAA;QACtK,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAE9B,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,IAAI,SAAS,CAAC,oBAAoB,IAAI,IAAI,EAAE,CAAC;YAC3C,MAAM,qBAAqB,GAAG,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAA,2BAAe,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAA;YAE1H,MAAM,SAAS,CAAC,oBAAoB,CAAC;gBACnC,QAAQ,EAAE,IAAI;gBACd,SAAS;gBACT,aAAa,EAAE,WAAW,IAAI,IAAI,IAAI,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,IAAA,uBAAW,EAAC,EAAE,aAAa,EAAE,qBAAqB,EAAE,CAAC;gBAC/H,YAAY;aACb,CAAC,CAAA;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;QACjF,MAAM,IAAA,uBAAS,EAAC,qBAAqB,EAAE,wBAAwB,CAAC,CAAA;QAChE,MAAM,IAAA,uBAAS,EAAC,iBAAiB,EAAE,wBAAwB,CAAC,CAAA;QAE5D,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC1C,OAAM;QACR,CAAC;QAED,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QAEtC,IAAI,SAAS,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,CAAA;QACxC,CAAC;QAED,MAAM,MAAM,GAAG,WAAW,IAAI,IAAI,CAAA;QAClC,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;QAC3D,IAAI,IAAI,EAAE,CAAC;YACT,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,IAAI,EAAE,4BAA4B,EAAE,OAAO,CAAC,CAAA;QAC1G,CAAC;IACH,CAAC;IAES,KAAK,CAAC,eAAe,CAAC,MAAc,EAAE,SAAiB,EAAE,YAAkC,EAAE,IAAU,EAAE,4BAAgC,EAAE,OAAsB;QACzK,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,4BAA4B,CAAC,CAAA;QAC/E,MAAM,MAAM,GAAG,WAAW,IAAI,IAAI,CAAA;QAClC,MAAM,WAAW,GAAG;YAClB,SAAS;YACT,MAAM;YACN,IAAI;YACJ,OAAO;YACP,QAAQ,EAAE,IAAI;YACd,oBAAoB,EAAE,YAAY;SACnC,CAAA;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QACvD,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAC9F,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAA;YAC/C,CAAC;iBAAM,CAAC;gBACN,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,qFAAqF,CAAC,CAAA;YACvG,CAAC;QACH,CAAC;IACH,CAAC;IAED,2BAA2B;IACjB,8BAA8B,CAAC,WAA6B;QACpE,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,YAAY,CAClB,WAA6B,EAC7B,WAA+B,EAC/B,YAAoB,EACpB,kBAA0B,EAC1B,WAA6B,EAC7B,4BAAgC,EAChC,eAAiC,EACjC,aAAqC;QAErC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,iBAAiB,GAAG,WAAW,IAAI,IAAI,IAAI,IAAA,uCAAqB,EAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEjF,MAAM,YAAY,GAAG,IAAA,iCAAmB,EAAC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,4BAA4B,EAAE,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;QAC9J,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;gBACnC,OAAO,CAAC,eAAe,GAAG,eAAe,CAAA;YAC3C,CAAC;QACH,CAAC;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,MAAM,WAAW,GAAG,IAAA,mCAAiB,EACnC,MAAM,EACN,MAAM,EACN,iBAAiB;YACf,CAAC,CAAC;gBACE,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACrC,IAAI,EAAE,8CAA8B;gBACpC,GAAG,MAAM,CAAC,aAAa;aACxB;YACH,CAAC,CAAC,MAAM,CAAC,aAAa,EACxB,SAAS,CAAC,iBAAiB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,iBAAiB,EAAE,CAC3E,CAAA;QAED,MAAM,gBAAgB,GAAG,CAAC,QAA4B,EAAE,EAAE;YACxD,OAAO,IAAA,+BAAe,EAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;gBAC/H,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,+BAA+B,EAAE,CAAC;oBAChF,MAAM,iBAAiB,GAAG,IAAA,sCAAwB,EAAC,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,4BAA4B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;oBACtI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,IAAA,yCAAyB,EAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAA;gBAClF,CAAC;gBACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YACjD,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACjC,WAAW,CAAC,OAAO,CAAC,sBAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,yBAAW,CAAC,MAAM,EAAE,YAAY,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,4BAAY,EAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,CAAA;QACrK,CAAC;aAAM,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YAC/B,+FAA+F;YAC/F,+DAA+D;YAC/D,0CAA0C;YAC1C,MAAM,wBAAwB,GAAG,IAAI,CAAC,8BAA8B,CAAC,WAAW,CAAC,CAAA;YACjF,MAAM,mBAAmB,GAAoB,IAAI,CAAC,EAAE;gBAClD,IAAI,wBAAwB,IAAI,IAAI,EAAE,CAAC;oBACrC,MAAM,MAAM,GAAG,wBAAwB,CAAC,IAAI,CAAC,CAAA;oBAC7C,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;wBACnB,OAAO,MAAM,CAAA;oBACf,CAAC;gBACH,CAAC;gBACD,OAAO,WAAW,CAAC,IAAI,CAAC,CAAA;YAC1B,CAAC,CAAA;YAED,WAAW,CAAC,OAAO,CAAC,sBAAe,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,IAAA,4BAAY,EAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAA;QACnI,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAG,IAAA,6BAAe,EAAC,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE;gBAC9E,aAAa;gBACb,kBAAkB,EAAE,4BAA4B;gBAChD,YAAY,EAAE,WAAW,CAAC,MAAM;gBAChC,UAAU,EAAE,MAAM;aACnB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,aAAa,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAA;YACnE,WAAW,CAAC,OAAO,CACjB,gBAAgB,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,EAAC,QAAQ,EAAC,EAAE;gBACnD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBAC/B,MAAM,IAAA,8BAAc,EAAC,WAAW,EAAE,OAAO,CAAC,CAAA;gBAC5C,CAAC;gBAED,MAAM,IAAI,uBAAY,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YACzI,CAAC,CAAC,CACH,CAAA;QACH,CAAC;IACH,CAAC;IAED,6DAA6D;IACnD,OAAO,CAAC,WAA6B,EAAE,MAAe;QAC9D,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,WAAW;QACT,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9B,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,kBAAsB;QACrD,IAAI,CAAC,IAAA,2BAAe,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAA;QACb,CAAC;QAED,SAAS,YAAY,CAAC,IAAY;YAChC,OAAO,GAAG,IAAI,wEAAwE,CAAA;QACxF,CAAC;QAED,MAAM,aAAa,GAAG,IAAI,CAAC,MAAa,CAAA;QACxC,IAAI,aAAa,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAA;QAC9C,CAAC;QACD,IAAI,aAAa,CAAC,iBAAiB,CAAC,IAAI,IAAI,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,IAAI,CAAA;QAChD,MAAM,MAAM,GAAG,gBAAgB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAA;QAC7E,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,MAAM,WAAW,GAAG,MAAM,IAAA,eAAU,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,CAAA;YAC7E,8BAA8B;YAC9B,IAAI,WAAW,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC;gBACjD,kBAAG,CAAC,IAAI,CACN;oBACE,QAAQ,EAAE,kFAAkF;iBAC7F,EACD,2DAA2D,CAC5D,CAAA;YACH,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;YACtC,OAAO,EAAE,CAAA;QACX,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC3C,IAAK,MAAc,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAA;YAC/C,CAAC;QACH,CAAC;QACD,OAAO,IAAA,yBAAU,EAAC,EAAE,EAAE,MAAM,CAAC,CAAA;IAC/B,CAAC;IAEM,iBAAiB,CAAC,IAAY;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAEM,yBAAyB,CAAC,SAAiB;QAChD,OAAO,SAAS,CAAA;IAClB,CAAC;IAED,eAAe,CAAC,SAAiB;QAC/B,IAAI,IAAI,CAAC,QAAQ,KAAK,gBAAQ,CAAC,GAAG,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CAAA;QAC7C,CAAC;aAAM,IAAI,IAAA,2BAAe,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAChD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAC1C,CAAC;aAAM,CAAC;YACN,OAAO,SAAS,CAAA;QAClB,CAAC;IACH,CAAC;IAEM,oBAAoB,CAAC,SAAiB;QAC3C,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,EAAE,UAAU,EAAE,WAAW,CAAC,CAAA;IAC7F,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,YAAoB,EAAE,IAAY,EAAE,aAAqB,EAAE,MAAe;QACzG,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAA;QAC1F,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,IAAA,oCAAkB,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,YAAY,EAAE,aAAa,CAAC,CAAA;YAC1F,OAAM;QACR,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QACnC,oHAAoH;QACpH,8FAA8F;QAC9F,IAAI,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACrC,4GAA4G;YAC5G,mGAAmG;YACnG,yCAAyC;YACzC,MAAM,SAAS,GAAkB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YAC/D,IAAI,iBAAiB,GAAG,CAAC,CAAA;YACzB,SAAS,CAAC,IAAI,CAAC,CAAC,QAAgB,EAAE,KAAa,EAAE,EAAE;gBACjD,iBAAiB,GAAG,KAAK,CAAA;gBACzB,OAAO,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACnC,CAAC,CAAC,CAAA;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAA;YACxE,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;YACtH,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,CAAA;YAChD,MAAM,IAAA,oCAAkB,EAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAA;QAC7F,CAAC;aAAM,CAAC;YACN,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,YAAY,CAAC,CAAA;YAC7D,MAAM,OAAO,GAAG,MAAM,IAAA,eAAU,EAAC,QAAQ,CAAC,CAAA;YAC1C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,KAAK,QAAQ,qDAAqD,CAAC,CAAA;YACrG,CAAC;iBAAM,CAAC;gBACN,8BAA8B;gBAC9B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;oBACtB,MAAM,IAAI,KAAK,CAAC,GAAG,aAAa,KAAK,QAAQ,oDAAoD,CAAC,CAAA;gBACpG,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,MAAe,EAAE,SAAoB;QACvF,MAAM,OAAO,GAAG,MAAM,IAAA,eAAU,EAAC,SAAS,CAAC,CAAA;QAC3C,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,qBAAqB,SAAS,qDAAqD,CAAC,CAAA;QACtG,CAAC;aAAM,CAAC;YACN,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,qBAAqB,SAAS,yDAAyD,CAAC,CAAA;YAC1G,CAAC;QACH,CAAC;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;QACpD,MAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,CAAA;QACvI,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,QAAQ,EAAE,wBAAwB,EAAE,MAAM,CAAC,CAAA;QACvF,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,CAAC,CAAA;IACpF,CAAC;IAED,uDAAuD;IACvD,uBAAuB,CACrB,aAA4B,EAC5B,GAAW,EACX,IAAkB,EAClB,eAAe,GAAG,IAAI,EACtB,WAAoB,EACpB,WAAW,GAAG,mCAAmC;QAEjD,OAAO,+BAA+B,CAAC,aAAa,EAAE,GAAG,EAAE,CACzD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,GAAG,EAAE,eAAe,IAAI,IAAI,KAAK,IAAA,4BAAqB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CACzH,CAAA;IACH,CAAC;IAED,yBAAyB,CACvB,qBAA+D,EAC/D,GAAW,EACX,IAAkB,EAClB,cAAuB,EACvB,eAAe,GAAG,IAAI,EACtB,WAAoB;QAEpB,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAA;QACnG,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,YAAY,IAAI,eAAe,IAAI,IAAI,KAAK,IAAA,4BAAqB,EAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAC9I,CAAC;IAED,qBAAqB,CAAC,qBAA+D,EAAE,cAAkC;QACvH,MAAM,oBAAoB,GAAG,CAAA,qBAAqB,aAArB,qBAAqB,uBAArB,qBAAqB,CAAE,YAAY,KAAI,IAAI,CAAC,4BAA4B,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;QAC9I,OAAO;YACL,YAAY,EAAE,CAAC,CAAC,oBAAoB;YACpC,OAAO,EAAE,oBAAoB,IAAI,cAAc,IAAI,0CAA0C;SAC9F,CAAA;IACH,CAAC;IAED,+BAA+B,CAAC,qBAA+D,EAAE,GAAW,EAAE,IAAkB;QAC9H,uDAAuD;QACvD,OAAO,IAAI,CAAC,yBAAyB,CAAC,qBAAqB,EAAE,GAAG,EAAE,IAAI,EAAE,0CAA0C,EAAE,IAAI,CAAC,CAAA;IAC3H,CAAC;IAEO,mBAAmB,CAAC,OAAY,EAAE,GAAW,EAAE,IAA6B;QAClF,MAAM,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,0BAAmB,EAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QACrE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,EAAE;YACzC,GAAG;SACJ,CAAC,CAAA;IACJ,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,IAAoB,EAAE,QAAa,EAAE,EAAE,sBAAsB,GAAG,IAAI;QAC/F,OAAO,IAAA,2BAAa,EAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,qBAAqB,EAAE,GAAG,KAAK,EAAE,EAAE,sBAAsB,CAAC,CAAA;IAClI,CAAC;IAED,aAAa,CAAC,GAAkB,EAAE,UAAqC,EAAE,UAAmB;QAC1F,MAAM,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAA;QAC3C,MAAM,SAAS,GAAG,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QAC3C,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,SAAS,GAAG,UAAU,EAAE,GAAG,MAAM,EAAE,CAAA;IAC/K,CAAC;IAED,WAAW,CAAC,MAAc;QACxB,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,CAAC,CAAA;IACzD,CAAC;IAED,IAAI,gBAAgB;QAClB,OAAO,IAAA,sBAAO,EAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,MAAM,CAAC,IAAA,sBAAO,EAAC,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAC,CAAA;IAClH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAiC,EAAE,GAAG,KAAoB;QAC1E,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAA;QAChD,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACzB,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAA;YAC5C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,CAAA;gBACtC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,IAAA,8BAAe,EAAC,MAAM,CAAC,EAAE,CAAC;YACtD,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,YAAY,CAAA;YAC5C,IAAI,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACxC,CAAC;YAED,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YAC1C,IAAI,CAAC,MAAM,IAAA,eAAU,EAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;gBAClC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;gBACzC,IAAI,CAAC,MAAM,IAAA,eAAU,EAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;oBAClC,MAAM,IAAI,wCAAyB,CACjC,mCAAmC,MAAM,uBAAuB,YAAY,wCAAwC,IAAI,CAAC,UAAU,IAAI,CACxI,CAAA;gBACH,CAAC;YACH,CAAC;YACD,OAAO,CAAC,CAAA;QACV,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,gBAAgB;QAClB,MAAM,wBAAwB,GAAG,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,CAAA;QACnF,OAAO,CAAC,wBAAwB,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,wBAAwB,CAAC,IAAI,KAAK,CAAA;IAC9G,CAAC;IAES,KAAK,CAAC,gBAAgB,CAAC,MAAkB;QACjD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAA,sBAAO,EAAC,IAAI,CAAC,4BAA4B,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAA;QACtH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;YACrC,IAAI,SAAS,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;gBACrC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAChD,CAAC;YAED,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,EAAE,WAAW,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;YACpH,OAAO,IAAI,CAAC,uBAAuB,EAAE,CAAA;QACvC,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QACvB,CAAC;IACH,CAAC;IAED,uBAAuB;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAA;QACrC,OAAO,SAAS,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC1F,CAAC;IAED,mJAAmJ;IACnJ,KAAK,CAAC,WAAW,CAAC,OAAsB,EAAE,eAA8B,EAAE,YAAwB;QAChG,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,MAAO,CAAC,CAAA;QACjE,MAAM,IAAI,GAAG;YACX,MAAM;YACN,UAAU;YACV,YAAY;YACZ,QAAQ;YACR,IAAI,CAAC,iBAAiB;YACtB,QAAQ;YACR,IAAI,CAAC,UAAU;YACf,OAAO;YACP,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,YAAY,EAAE,CAAC;SAC/D,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAA;QAC9B,CAAC;QACD,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAA;QACvC,CAAC;QAED,MAAM,MAAM,GAAsB,MAAM,IAAA,oCAAuB,EAAC,IAAI,CAAC,CAAA;QACrE,MAAM,YAAY,GAAG,MAAM,CAAC,KAAK,CAAA;QACjC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,wCAAyB,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,CAAA;QACrE,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;YACtB,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,6BAA6B,EAAE,EAAE,WAAW,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QAChI,CAAC;QAED,OAAO,MAAM,CAAC,KAAK,IAAI,EAAE,CAAA;IAC3B,CAAC;CACF;AAzqBD,4CAyqBC;AAiBD,SAAgB,gBAAgB,CAAC,IAAY;IAC3C,OAAO,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACvC,CAAC;AAFD,4CAEC;AAED,SAAgB,+BAA+B,CAAC,aAA4B,EAAE,gBAA8B;IAC1G,0DAA0D;IAC1D,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAI,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,OAAO,IAAI,CAAA;QACb,CAAC;QAED,mGAAmG;QACnG,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;QAChD,IAAI,gBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,OAAO,aAAa,CAAA;QACtB,CAAC;IACH,CAAC;IAED,OAAO,gBAAgB,EAAE,CAAA;AAC3B,CAAC;AAfD,0EAeC;AAED,qBAAqB;AACrB,SAAgB,YAAY,CAAC,GAAW;IACtC,OAAO,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;AACrD,CAAC;AAFD,oCAEC;AAED,KAAK,UAAU,aAAa,CAAI,IAAwB,EAAE,IAAY;IACpE,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAA;IAClD,MAAM,YAAY,GAAG,IAAI,KAAK,QAAQ,CAAA;IACtC,IAAI,CAAC;QACH,IAAI,SAAS,KAAK,MAAM,IAAI,CAAC,SAAS,KAAK,KAAK,IAAI,YAAY,CAAC,EAAE,CAAC;YAClE,MAAM,OAAO,GAAG,IAAA,mBAAa,EAAC,IAAI,CAAC,CAAC,IAAI,CAAA;YACxC,OAAO,MAAM,IAAI,CAAC,UAAU,GAAG,OAAO,GAAG,IAAI,CAAC,CAAA;QAChD,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,kBAAG,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,8DAA8D,CAAC,CAAA;IACjG,CAAC;IACD,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;AACtB,CAAC;AAEM,KAAK,UAAU,eAAe,CAAI,IAAwB,EAAE,QAAoB,EAAE,IAAY;IACnG,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACrD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,IAAI,CAAC,GAAG,QAAkB,CAAA;IAC1B,IAAI,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACtB,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,IAAI,CAAC;QACH,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACxB,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAA,oBAAK,EAAC,CAAC,CAAC,CAAA;QACR,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,GAAQ,MAAM,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;IAC3C,MAAM,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,CAAA;IAC3B,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;QACxB,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,CAAA;IACvB,CAAC;SAAM,CAAC;QACN,OAAO,WAAW,CAAA;IACpB,CAAC;AACH,CAAC;AAxBD,0CAwBC;AAED,SAAgB,aAAa,CAAC,EAA6B,EAAE,EAA6B;IACxF,OAAO,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;AAC7B,CAAC;AAFD,sCAEC;AAED,SAAS,qBAAqB,CAAC,IAAY;IACzC,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;AACrD,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { Arch, asArray, AsyncTaskManager, debug, DebugLogger, deepAssign, getArchSuffix, InvalidConfigurationError, isEmptyOrSpaces, log } from \"builder-util\"\nimport { defaultArchFromString, getArtifactArchName } from \"builder-util/out/arch\"\nimport { FileTransformer, statOrNull } from \"builder-util/out/fs\"\nimport { orIfFileNotExist } from \"builder-util/out/promise\"\nimport { readdir } from \"fs/promises\"\nimport { Lazy } from \"lazy-val\"\nimport { Minimatch } from \"minimatch\"\nimport * as path from \"path\"\nimport { pathToFileURL } from \"url\"\nimport { AppInfo } from \"./appInfo\"\nimport { checkFileInArchive } from \"./asar/asarFileChecker\"\nimport { AsarPackager } from \"./asar/asarUtil\"\nimport { computeData } from \"./asar/integrity\"\nimport { copyFiles, FileMatcher, getFileMatchers, GetFileMatchersOptions, getMainFileMatchers, getNodeModuleFileMatcher } from \"./fileMatcher\"\nimport { createTransformer, isElectronCompileUsed } from \"./fileTransformer\"\nimport { Framework, isElectronBased } from \"./Framework\"\nimport {\n  AfterPackContext,\n  AsarOptions,\n  CompressionLevel,\n  Configuration,\n  ElectronPlatformName,\n  FileAssociation,\n  Packager,\n  PackagerOptions,\n  Platform,\n  PlatformSpecificBuildOptions,\n  Target,\n  TargetSpecificOptions,\n} from \"./index\"\nimport { executeAppBuilderAsJson } from \"./util/appBuilder\"\nimport { computeFileSets, computeNodeModuleFileSets, copyAppFiles, ELECTRON_COMPILE_SHIM_FILENAME, transformFiles } from \"./util/appFileCopier\"\nimport { expandMacro as doExpandMacro } from \"./util/macroExpander\"\n\nexport abstract class PlatformPackager<DC extends PlatformSpecificBuildOptions> {\n  get packagerOptions(): PackagerOptions {\n    return this.info.options\n  }\n\n  get buildResourcesDir(): string {\n    return this.info.buildResourcesDir\n  }\n\n  get projectDir(): string {\n    return this.info.projectDir\n  }\n\n  get config(): Configuration {\n    return this.info.config\n  }\n\n  readonly platformSpecificBuildOptions: DC\n\n  get resourceList(): Promise<Array<string>> {\n    return this._resourceList.value\n  }\n\n  private readonly _resourceList = new Lazy<Array<string>>(() => orIfFileNotExist(readdir(this.info.buildResourcesDir), []))\n\n  readonly appInfo: AppInfo\n\n  protected constructor(\n    readonly info: Packager,\n    readonly platform: Platform\n  ) {\n    this.platformSpecificBuildOptions = PlatformPackager.normalizePlatformSpecificBuildOptions((this.config as any)[platform.buildConfigurationKey])\n    this.appInfo = this.prepareAppInfo(info.appInfo)\n  }\n\n  get compression(): CompressionLevel {\n    const compression = this.platformSpecificBuildOptions.compression\n    // explicitly set to null - request to use default value instead of parent (in the config)\n    if (compression === null) {\n      return \"normal\"\n    }\n    return compression || this.config.compression || \"normal\"\n  }\n\n  get debugLogger(): DebugLogger {\n    return this.info.debugLogger\n  }\n\n  abstract get defaultTarget(): Array<string>\n\n  // eslint-disable-next-line\n  protected prepareAppInfo(appInfo: AppInfo) {\n    return new AppInfo(this.info, null, this.platformSpecificBuildOptions)\n  }\n\n  private static normalizePlatformSpecificBuildOptions(options: any | null | undefined): any {\n    return options == null ? Object.create(null) : options\n  }\n\n  abstract createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void\n\n  protected getCscPassword(): string {\n    const password = this.doGetCscPassword()\n    if (isEmptyOrSpaces(password)) {\n      log.info({ reason: \"CSC_KEY_PASSWORD is not defined\" }, \"empty password will be used for code signing\")\n      return \"\"\n    } else {\n      return password.trim()\n    }\n  }\n\n  protected getCscLink(extraEnvName?: string | null): string | null | undefined {\n    // allow to specify as empty string\n    const envValue = chooseNotNull(extraEnvName == null ? null : process.env[extraEnvName], process.env.CSC_LINK)\n    return chooseNotNull(chooseNotNull(this.info.config.cscLink, this.platformSpecificBuildOptions.cscLink), envValue)\n  }\n\n  protected doGetCscPassword(): string | null | undefined {\n    // allow to specify as empty string\n    return chooseNotNull(chooseNotNull(this.info.config.cscKeyPassword, this.platformSpecificBuildOptions.cscKeyPassword), process.env.CSC_KEY_PASSWORD)\n  }\n\n  protected computeAppOutDir(outDir: string, arch: Arch): string {\n    return (\n      this.packagerOptions.prepackaged ||\n      path.join(\n        outDir,\n        `${this.platform.buildConfigurationKey}${getArchSuffix(arch, this.platformSpecificBuildOptions.defaultArch)}${this.platform === Platform.MAC ? \"\" : \"-unpacked\"}`\n      )\n    )\n  }\n\n  dispatchArtifactCreated(file: string, target: Target | null, arch: Arch | null, safeArtifactName?: string | null): Promise<void> {\n    return this.info.callArtifactBuildCompleted({\n      file,\n      safeArtifactName,\n      target,\n      arch,\n      packager: this,\n    })\n  }\n\n  async pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<any> {\n    const appOutDir = this.computeAppOutDir(outDir, arch)\n    await this.doPack(outDir, appOutDir, this.platform.nodeName as ElectronPlatformName, arch, this.platformSpecificBuildOptions, targets)\n    this.packageInDistributableFormat(appOutDir, arch, targets, taskManager)\n  }\n\n  protected packageInDistributableFormat(appOutDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): void {\n    if (targets.find(it => !it.isAsyncSupported) == null) {\n      PlatformPackager.buildAsyncTargets(targets, taskManager, appOutDir, arch)\n      return\n    }\n\n    taskManager.add(async () => {\n      // BluebirdPromise.map doesn't invoke target.build immediately, but for RemoteTarget it is very critical to call build() before finishBuild()\n      const subTaskManager = new AsyncTaskManager(this.info.cancellationToken)\n      PlatformPackager.buildAsyncTargets(targets, subTaskManager, appOutDir, arch)\n      await subTaskManager.awaitTasks()\n\n      for (const target of targets) {\n        if (!target.isAsyncSupported) {\n          await target.build(appOutDir, arch)\n        }\n      }\n    })\n  }\n\n  private static buildAsyncTargets(targets: Array<Target>, taskManager: AsyncTaskManager, appOutDir: string, arch: Arch) {\n    for (const target of targets) {\n      if (target.isAsyncSupported) {\n        taskManager.addTask(target.build(appOutDir, arch))\n      }\n    }\n  }\n\n  private getExtraFileMatchers(isResources: boolean, appOutDir: string, options: GetFileMatchersOptions): Array<FileMatcher> | null {\n    const base = isResources\n      ? this.getResourcesDir(appOutDir)\n      : this.platform === Platform.MAC\n        ? path.join(appOutDir, `${this.appInfo.productFilename}.app`, \"Contents\")\n        : appOutDir\n    return getFileMatchers(this.config, isResources ? \"extraResources\" : \"extraFiles\", base, options)\n  }\n\n  createGetFileMatchersOptions(outDir: string, arch: Arch, customBuildOptions: PlatformSpecificBuildOptions): GetFileMatchersOptions {\n    return {\n      macroExpander: it => this.expandMacro(it, arch == null ? null : Arch[arch], { \"/*\": \"{,/**/*}\" }),\n      customBuildOptions,\n      globalOutDir: outDir,\n      defaultSrc: this.projectDir,\n    }\n  }\n\n  protected async doPack(\n    outDir: string,\n    appOutDir: string,\n    platformName: ElectronPlatformName,\n    arch: Arch,\n    platformSpecificBuildOptions: DC,\n    targets: Array<Target>,\n    sign = true,\n    disableAsarIntegrity = false\n  ) {\n    if (this.packagerOptions.prepackaged != null) {\n      return\n    }\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    // Due to node-gyp rewriting GYP_MSVS_VERSION when reused across the same session, we must reset the env var: https://github.com/electron-userland/electron-builder/issues/7256\n    delete process.env.GYP_MSVS_VERSION\n\n    const beforePack = await resolveFunction(this.appInfo.type, this.config.beforePack, \"beforePack\")\n    if (beforePack != null) {\n      await beforePack({\n        appOutDir,\n        outDir,\n        arch,\n        targets,\n        packager: this,\n        electronPlatformName: platformName,\n      })\n    }\n\n    await this.info.installAppDependencies(this.platform, arch)\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    const framework = this.info.framework\n    log.info(\n      {\n        platform: platformName,\n        arch: Arch[arch],\n        [`${framework.name}`]: framework.version,\n        appOutDir: log.filePath(appOutDir),\n      },\n      `packaging`\n    )\n\n    await framework.prepareApplicationStageDirectory({\n      packager: this,\n      appOutDir,\n      platformName,\n      arch: Arch[arch],\n      version: framework.version,\n    })\n\n    const excludePatterns: Array<Minimatch> = []\n\n    const computeParsedPatterns = (patterns: Array<FileMatcher> | null) => {\n      if (patterns != null) {\n        for (const pattern of patterns) {\n          pattern.computeParsedPatterns(excludePatterns, this.info.projectDir)\n        }\n      }\n    }\n\n    const getFileMatchersOptions = this.createGetFileMatchersOptions(outDir, arch, platformSpecificBuildOptions)\n    const macroExpander = getFileMatchersOptions.macroExpander\n    const extraResourceMatchers = this.getExtraFileMatchers(true, appOutDir, getFileMatchersOptions)\n    computeParsedPatterns(extraResourceMatchers)\n    const extraFileMatchers = this.getExtraFileMatchers(false, appOutDir, getFileMatchersOptions)\n    computeParsedPatterns(extraFileMatchers)\n\n    const packContext: AfterPackContext = {\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName,\n    }\n\n    const asarOptions = await this.computeAsarOptions(platformSpecificBuildOptions)\n    const resourcesPath =\n      this.platform === Platform.MAC\n        ? path.join(appOutDir, framework.distMacOsAppName, \"Contents\", \"Resources\")\n        : isElectronBased(framework)\n          ? path.join(appOutDir, \"resources\")\n          : appOutDir\n    const taskManager = new AsyncTaskManager(this.info.cancellationToken)\n    this.copyAppFiles(taskManager, asarOptions, resourcesPath, path.join(resourcesPath, \"app\"), packContext, platformSpecificBuildOptions, excludePatterns, macroExpander)\n    await taskManager.awaitTasks()\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    if (framework.beforeCopyExtraFiles != null) {\n      const resourcesRelativePath = this.platform === Platform.MAC ? \"Resources\" : isElectronBased(framework) ? \"resources\" : \"\"\n\n      await framework.beforeCopyExtraFiles({\n        packager: this,\n        appOutDir,\n        asarIntegrity: asarOptions == null || disableAsarIntegrity ? null : await computeData({ resourcesPath, resourcesRelativePath }),\n        platformName,\n      })\n    }\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    const transformerForExtraFiles = this.createTransformerForExtraFiles(packContext)\n    await copyFiles(extraResourceMatchers, transformerForExtraFiles)\n    await copyFiles(extraFileMatchers, transformerForExtraFiles)\n\n    if (this.info.cancellationToken.cancelled) {\n      return\n    }\n\n    await this.info.afterPack(packContext)\n\n    if (framework.afterPack != null) {\n      await framework.afterPack(packContext)\n    }\n\n    const isAsar = asarOptions != null\n    await this.sanityCheckPackage(appOutDir, isAsar, framework)\n    if (sign) {\n      await this.doSignAfterPack(outDir, appOutDir, platformName, arch, platformSpecificBuildOptions, targets)\n    }\n  }\n\n  protected async doSignAfterPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: DC, targets: Array<Target>) {\n    const asarOptions = await this.computeAsarOptions(platformSpecificBuildOptions)\n    const isAsar = asarOptions != null\n    const packContext = {\n      appOutDir,\n      outDir,\n      arch,\n      targets,\n      packager: this,\n      electronPlatformName: platformName,\n    }\n    const didSign = await this.signApp(packContext, isAsar)\n    const afterSign = await resolveFunction(this.appInfo.type, this.config.afterSign, \"afterSign\")\n    if (afterSign != null) {\n      if (didSign) {\n        await Promise.resolve(afterSign(packContext))\n      } else {\n        log.warn(null, `skipping \"afterSign\" hook as no signing occurred, perhaps you intended \"afterPack\"?`)\n      }\n    }\n  }\n\n  // eslint-disable-next-line\n  protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null {\n    return null\n  }\n\n  private copyAppFiles(\n    taskManager: AsyncTaskManager,\n    asarOptions: AsarOptions | null,\n    resourcePath: string,\n    defaultDestination: string,\n    packContext: AfterPackContext,\n    platformSpecificBuildOptions: DC,\n    excludePatterns: Array<Minimatch>,\n    macroExpander: (it: string) => string\n  ) {\n    const appDir = this.info.appDir\n    const config = this.config\n    const isElectronCompile = asarOptions != null && isElectronCompileUsed(this.info)\n\n    const mainMatchers = getMainFileMatchers(appDir, defaultDestination, macroExpander, platformSpecificBuildOptions, this, packContext.outDir, isElectronCompile)\n    if (excludePatterns.length > 0) {\n      for (const matcher of mainMatchers) {\n        matcher.excludePatterns = excludePatterns\n      }\n    }\n\n    const framework = this.info.framework\n    const transformer = createTransformer(\n      appDir,\n      config,\n      isElectronCompile\n        ? {\n            originalMain: this.info.metadata.main,\n            main: ELECTRON_COMPILE_SHIM_FILENAME,\n            ...config.extraMetadata,\n          }\n        : config.extraMetadata,\n      framework.createTransformer == null ? null : framework.createTransformer()\n    )\n\n    const _computeFileSets = (matchers: Array<FileMatcher>) => {\n      return computeFileSets(matchers, this.info.isPrepackedAppAsar ? null : transformer, this, isElectronCompile).then(async result => {\n        if (!this.info.isPrepackedAppAsar && !this.info.areNodeModulesHandledExternally) {\n          const moduleFileMatcher = getNodeModuleFileMatcher(appDir, defaultDestination, macroExpander, platformSpecificBuildOptions, this.info)\n          result = result.concat(await computeNodeModuleFileSets(this, moduleFileMatcher))\n        }\n        return result.filter(it => it.files.length > 0)\n      })\n    }\n\n    if (this.info.isPrepackedAppAsar) {\n      taskManager.addTask(BluebirdPromise.each(_computeFileSets([new FileMatcher(appDir, resourcePath, macroExpander)]), it => copyAppFiles(it, this.info, transformer)))\n    } else if (asarOptions == null) {\n      // for ASAR all asar unpacked files will be extra transformed (e.g. sign of EXE and DLL) later,\n      // for prepackaged asar extra transformation not supported yet,\n      // so, extra transform if asar is disabled\n      const transformerForExtraFiles = this.createTransformerForExtraFiles(packContext)\n      const combinedTransformer: FileTransformer = file => {\n        if (transformerForExtraFiles != null) {\n          const result = transformerForExtraFiles(file)\n          if (result != null) {\n            return result\n          }\n        }\n        return transformer(file)\n      }\n\n      taskManager.addTask(BluebirdPromise.each(_computeFileSets(mainMatchers), it => copyAppFiles(it, this.info, combinedTransformer)))\n    } else {\n      const unpackPattern = getFileMatchers(config, \"asarUnpack\", defaultDestination, {\n        macroExpander,\n        customBuildOptions: platformSpecificBuildOptions,\n        globalOutDir: packContext.outDir,\n        defaultSrc: appDir,\n      })\n      const fileMatcher = unpackPattern == null ? null : unpackPattern[0]\n      taskManager.addTask(\n        _computeFileSets(mainMatchers).then(async fileSets => {\n          for (const fileSet of fileSets) {\n            await transformFiles(transformer, fileSet)\n          }\n\n          await new AsarPackager(appDir, resourcePath, asarOptions, fileMatcher == null ? null : fileMatcher.createFilter()).pack(fileSets, this)\n        })\n      )\n    }\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected signApp(packContext: AfterPackContext, isAsar: boolean): Promise<boolean> {\n    return Promise.resolve(false)\n  }\n\n  getIconPath(): Promise<string | null> {\n    return Promise.resolve(null)\n  }\n\n  private async computeAsarOptions(customBuildOptions: DC): Promise<AsarOptions | null> {\n    if (!isElectronBased(this.info.framework)) {\n      return null\n    }\n\n    function errorMessage(name: string) {\n      return `${name} is deprecated is deprecated and not supported — please use asarUnpack`\n    }\n\n    const buildMetadata = this.config as any\n    if (buildMetadata[\"asar-unpack\"] != null) {\n      throw new Error(errorMessage(\"asar-unpack\"))\n    }\n    if (buildMetadata[\"asar-unpack-dir\"] != null) {\n      throw new Error(errorMessage(\"asar-unpack-dir\"))\n    }\n\n    const platformSpecific = customBuildOptions.asar\n    const result = platformSpecific == null ? this.config.asar : platformSpecific\n    if (result === false) {\n      const appAsarStat = await statOrNull(path.join(this.info.appDir, \"app.asar\"))\n      //noinspection ES6MissingAwait\n      if (appAsarStat == null || !appAsarStat.isFile()) {\n        log.warn(\n          {\n            solution: \"enable asar and use asarUnpack to unpack files that must be externally available\",\n          },\n          \"asar usage is disabled — this is strongly not recommended\"\n        )\n      }\n      return null\n    }\n\n    if (result == null || result === true) {\n      return {}\n    }\n\n    for (const name of [\"unpackDir\", \"unpack\"]) {\n      if ((result as any)[name] != null) {\n        throw new Error(errorMessage(`asar.${name}`))\n      }\n    }\n    return deepAssign({}, result)\n  }\n\n  public getElectronSrcDir(dist: string): string {\n    return path.resolve(this.projectDir, dist)\n  }\n\n  public getElectronDestinationDir(appOutDir: string): string {\n    return appOutDir\n  }\n\n  getResourcesDir(appOutDir: string): string {\n    if (this.platform === Platform.MAC) {\n      return this.getMacOsResourcesDir(appOutDir)\n    } else if (isElectronBased(this.info.framework)) {\n      return path.join(appOutDir, \"resources\")\n    } else {\n      return appOutDir\n    }\n  }\n\n  public getMacOsResourcesDir(appOutDir: string): string {\n    return path.join(appOutDir, `${this.appInfo.productFilename}.app`, \"Contents\", \"Resources\")\n  }\n\n  private async checkFileInPackage(resourcesDir: string, file: string, messagePrefix: string, isAsar: boolean) {\n    const relativeFile = path.relative(this.info.appDir, path.resolve(this.info.appDir, file))\n    if (isAsar) {\n      await checkFileInArchive(path.join(resourcesDir, \"app.asar\"), relativeFile, messagePrefix)\n      return\n    }\n\n    const pathParsed = path.parse(file)\n    // Even when packaging to asar is disabled, it does not imply that the main file can not be inside an .asar archive.\n    // This may occur when the packaging is done manually before processing with electron-builder.\n    if (pathParsed.dir.includes(\".asar\")) {\n      // The path needs to be split to the part with an asar archive which acts like a directory and the part with\n      // the path to main file itself. (e.g. path/arch.asar/dir/index.js -> path/arch.asar, dir/index.js)\n      // noinspection TypeScriptValidateJSTypes\n      const pathSplit: Array<string> = pathParsed.dir.split(path.sep)\n      let partWithAsarIndex = 0\n      pathSplit.some((pathPart: string, index: number) => {\n        partWithAsarIndex = index\n        return pathPart.endsWith(\".asar\")\n      })\n      const asarPath = path.join(...pathSplit.slice(0, partWithAsarIndex + 1))\n      let mainPath = pathSplit.length > partWithAsarIndex + 1 ? path.join.apply(pathSplit.slice(partWithAsarIndex + 1)) : \"\"\n      mainPath += path.join(mainPath, pathParsed.base)\n      await checkFileInArchive(path.join(resourcesDir, \"app\", asarPath), mainPath, messagePrefix)\n    } else {\n      const fullPath = path.join(resourcesDir, \"app\", relativeFile)\n      const outStat = await statOrNull(fullPath)\n      if (outStat == null) {\n        throw new Error(`${messagePrefix} \"${fullPath}\" does not exist. Seems like a wrong configuration.`)\n      } else {\n        //noinspection ES6MissingAwait\n        if (!outStat.isFile()) {\n          throw new Error(`${messagePrefix} \"${fullPath}\" is not a file. Seems like a wrong configuration.`)\n        }\n      }\n    }\n  }\n\n  private async sanityCheckPackage(appOutDir: string, isAsar: boolean, framework: Framework): Promise<any> {\n    const outStat = await statOrNull(appOutDir)\n    if (outStat == null) {\n      throw new Error(`Output directory \"${appOutDir}\" does not exist. Seems like a wrong configuration.`)\n    } else {\n      //noinspection ES6MissingAwait\n      if (!outStat.isDirectory()) {\n        throw new Error(`Output directory \"${appOutDir}\" is not a directory. Seems like a wrong configuration.`)\n      }\n    }\n\n    const resourcesDir = this.getResourcesDir(appOutDir)\n    const mainFile = (framework.getMainFile == null ? null : framework.getMainFile(this.platform)) || this.info.metadata.main || \"index.js\"\n    await this.checkFileInPackage(resourcesDir, mainFile, \"Application entry file\", isAsar)\n    await this.checkFileInPackage(resourcesDir, \"package.json\", \"Application\", isAsar)\n  }\n\n  // tslint:disable-next-line:no-invalid-template-strings\n  computeSafeArtifactName(\n    suggestedName: string | null,\n    ext: string,\n    arch?: Arch | null,\n    skipDefaultArch = true,\n    defaultArch?: string,\n    safePattern = \"${name}-${version}-${arch}.${ext}\"\n  ): string | null {\n    return computeSafeArtifactNameIfNeeded(suggestedName, () =>\n      this.computeArtifactName(safePattern, ext, skipDefaultArch && arch === defaultArchFromString(defaultArch) ? null : arch)\n    )\n  }\n\n  expandArtifactNamePattern(\n    targetSpecificOptions: TargetSpecificOptions | null | undefined,\n    ext: string,\n    arch?: Arch | null,\n    defaultPattern?: string,\n    skipDefaultArch = true,\n    defaultArch?: string\n  ): string {\n    const { pattern, isUserForced } = this.artifactPatternConfig(targetSpecificOptions, defaultPattern)\n    return this.computeArtifactName(pattern, ext, !isUserForced && skipDefaultArch && arch === defaultArchFromString(defaultArch) ? null : arch)\n  }\n\n  artifactPatternConfig(targetSpecificOptions: TargetSpecificOptions | null | undefined, defaultPattern: string | undefined) {\n    const userSpecifiedPattern = targetSpecificOptions?.artifactName || this.platformSpecificBuildOptions.artifactName || this.config.artifactName\n    return {\n      isUserForced: !!userSpecifiedPattern,\n      pattern: userSpecifiedPattern || defaultPattern || \"${productName}-${version}-${arch}.${ext}\",\n    }\n  }\n\n  expandArtifactBeautyNamePattern(targetSpecificOptions: TargetSpecificOptions | null | undefined, ext: string, arch?: Arch | null): string {\n    // tslint:disable-next-line:no-invalid-template-strings\n    return this.expandArtifactNamePattern(targetSpecificOptions, ext, arch, \"${productName} ${version} ${arch}.${ext}\", true)\n  }\n\n  private computeArtifactName(pattern: any, ext: string, arch: Arch | null | undefined): string {\n    const archName = arch == null ? null : getArtifactArchName(arch, ext)\n    return this.expandMacro(pattern, archName, {\n      ext,\n    })\n  }\n\n  expandMacro(pattern: string, arch?: string | null, extra: any = {}, isProductNameSanitized = true): string {\n    return doExpandMacro(pattern, arch, this.appInfo, { os: this.platform.buildConfigurationKey, ...extra }, isProductNameSanitized)\n  }\n\n  generateName2(ext: string | null, classifier: string | null | undefined, deployment: boolean): string {\n    const dotExt = ext == null ? \"\" : `.${ext}`\n    const separator = ext === \"deb\" ? \"_\" : \"-\"\n    return `${deployment ? this.appInfo.name : this.appInfo.productFilename}${separator}${this.appInfo.version}${classifier == null ? \"\" : `${separator}${classifier}`}${dotExt}`\n  }\n\n  getTempFile(suffix: string): Promise<string> {\n    return this.info.tempDirManager.getTempFile({ suffix })\n  }\n\n  get fileAssociations(): Array<FileAssociation> {\n    return asArray(this.config.fileAssociations).concat(asArray(this.platformSpecificBuildOptions.fileAssociations))\n  }\n\n  async getResource(custom: string | null | undefined, ...names: Array<string>): Promise<string | null> {\n    const resourcesDir = this.info.buildResourcesDir\n    if (custom === undefined) {\n      const resourceList = await this.resourceList\n      for (const name of names) {\n        if (resourceList.includes(name)) {\n          return path.join(resourcesDir, name)\n        }\n      }\n    } else if (custom != null && !isEmptyOrSpaces(custom)) {\n      const resourceList = await this.resourceList\n      if (resourceList.includes(custom)) {\n        return path.join(resourcesDir, custom)\n      }\n\n      let p = path.resolve(resourcesDir, custom)\n      if ((await statOrNull(p)) == null) {\n        p = path.resolve(this.projectDir, custom)\n        if ((await statOrNull(p)) == null) {\n          throw new InvalidConfigurationError(\n            `cannot find specified resource \"${custom}\", nor relative to \"${resourcesDir}\", neither relative to project dir (\"${this.projectDir}\")`\n          )\n        }\n      }\n      return p\n    }\n    return null\n  }\n\n  get forceCodeSigning(): boolean {\n    const forceCodeSigningPlatform = this.platformSpecificBuildOptions.forceCodeSigning\n    return (forceCodeSigningPlatform == null ? this.config.forceCodeSigning : forceCodeSigningPlatform) || false\n  }\n\n  protected async getOrConvertIcon(format: IconFormat): Promise<string | null> {\n    const result = await this.resolveIcon(asArray(this.platformSpecificBuildOptions.icon || this.config.icon), [], format)\n    if (result.length === 0) {\n      const framework = this.info.framework\n      if (framework.getDefaultIcon != null) {\n        return framework.getDefaultIcon(this.platform)\n      }\n\n      log.warn({ reason: \"application icon is not set\" }, `default ${capitalizeFirstLetter(framework.name)} icon is used`)\n      return this.getDefaultFrameworkIcon()\n    } else {\n      return result[0].file\n    }\n  }\n\n  getDefaultFrameworkIcon(): string | null {\n    const framework = this.info.framework\n    return framework.getDefaultIcon == null ? null : framework.getDefaultIcon(this.platform)\n  }\n\n  // convert if need, validate size (it is a reason why tool is called even if file has target extension (already specified as foo.icns for example))\n  async resolveIcon(sources: Array<string>, fallbackSources: Array<string>, outputFormat: IconFormat): Promise<Array<IconInfo>> {\n    const output = this.expandMacro(this.config.directories!.output!)\n    const args = [\n      \"icon\",\n      \"--format\",\n      outputFormat,\n      \"--root\",\n      this.buildResourcesDir,\n      \"--root\",\n      this.projectDir,\n      \"--out\",\n      path.resolve(this.projectDir, output, `.icon-${outputFormat}`),\n    ]\n    for (const source of sources) {\n      args.push(\"--input\", source)\n    }\n    for (const source of fallbackSources) {\n      args.push(\"--fallback-input\", source)\n    }\n\n    const result: IconConvertResult = await executeAppBuilderAsJson(args)\n    const errorMessage = result.error\n    if (errorMessage != null) {\n      throw new InvalidConfigurationError(errorMessage, result.errorCode)\n    }\n\n    if (result.isFallback) {\n      log.warn({ reason: \"application icon is not set\" }, `default ${capitalizeFirstLetter(this.info.framework.name)} icon is used`)\n    }\n\n    return result.icons || []\n  }\n}\n\nexport interface IconInfo {\n  file: string\n  size: number\n}\n\ninterface IconConvertResult {\n  icons?: Array<IconInfo>\n\n  error?: string\n  errorCode?: string\n  isFallback?: boolean\n}\n\nexport type IconFormat = \"icns\" | \"ico\" | \"set\"\n\nexport function isSafeGithubName(name: string) {\n  return /^[0-9A-Za-z._-]+$/.test(name)\n}\n\nexport function computeSafeArtifactNameIfNeeded(suggestedName: string | null, safeNameProducer: () => string): string | null {\n  // GitHub only allows the listed characters in file names.\n  if (suggestedName != null) {\n    if (isSafeGithubName(suggestedName)) {\n      return null\n    }\n\n    // prefer to use suggested name - so, if space is the only problem, just replace only space to dash\n    suggestedName = suggestedName.replace(/ /g, \"-\")\n    if (isSafeGithubName(suggestedName)) {\n      return suggestedName\n    }\n  }\n\n  return safeNameProducer()\n}\n\n// remove leading dot\nexport function normalizeExt(ext: string) {\n  return ext.startsWith(\".\") ? ext.substring(1) : ext\n}\n\nasync function resolveModule<T>(type: string | undefined, name: string): Promise<T> {\n  const extension = path.extname(name).toLowerCase()\n  const isModuleType = type === \"module\"\n  try {\n    if (extension === \".mjs\" || (extension === \".js\" && isModuleType)) {\n      const fileUrl = pathToFileURL(name).href\n      return await eval(\"import('\" + fileUrl + \"')\")\n    }\n  } catch (error) {\n    log.debug({ moduleName: name }, \"Unable to dynamically import hook, falling back to `require`\")\n  }\n  return require(name)\n}\n\nexport async function resolveFunction<T>(type: string | undefined, executor: T | string, name: string): Promise<T> {\n  if (executor == null || typeof executor !== \"string\") {\n    return executor\n  }\n\n  let p = executor as string\n  if (p.startsWith(\".\")) {\n    p = path.resolve(p)\n  }\n\n  try {\n    p = require.resolve(p)\n  } catch (e: any) {\n    debug(e)\n    p = path.resolve(p)\n  }\n\n  const m: any = await resolveModule(type, p)\n  const namedExport = m[name]\n  if (namedExport == null) {\n    return m.default || m\n  } else {\n    return namedExport\n  }\n}\n\nexport function chooseNotNull(v1: string | null | undefined, v2: string | null | undefined): string | null | undefined {\n  return v1 == null ? v2 : v1\n}\n\nfunction capitalizeFirstLetter(text: string) {\n  return text.charAt(0).toUpperCase() + text.slice(1)\n}\n"]}
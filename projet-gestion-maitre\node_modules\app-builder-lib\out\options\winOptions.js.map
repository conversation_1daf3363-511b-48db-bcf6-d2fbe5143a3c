{"version": 3, "file": "winOptions.js", "sourceRoot": "", "sources": ["../../src/options/winOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { PlatformSpecificBuildOptions, TargetConfigType } from \"../index\"\nimport { CustomWindowsSign } from \"../codeSign/windowsCodeSign\"\n\nexport interface WindowsConfiguration extends PlatformSpecificBuildOptions {\n  /**\n   * The target package type: list of `nsis`, `nsis-web` (Web installer), `portable` ([portable](/configuration/nsis#portable) app without installation), `appx`, `msi`, `msi-wrapped`, `squirrel`, `7z`, `zip`, `tar.xz`, `tar.lz`, `tar.gz`, `tar.bz2`, `dir`.\n   * AppX package can be built only on Windows 10.\n   *\n   * To use Squirrel.Windows please install `electron-builder-squirrel-windows` dependency.\n   *\n   * @default nsis\n   */\n  readonly target?: TargetConfigType\n\n  /**\n   * The path to application icon.\n   * @default build/icon.ico\n   */\n  readonly icon?: string | null\n\n  /**\n   * The trademarks and registered trademarks.\n   */\n  readonly legalTrademarks?: string | null\n\n  /**\n   * Array of signing algorithms used. For AppX `sha256` is always used.\n   * @default ['sha1', 'sha256']\n   */\n  readonly signingHashAlgorithms?: Array<\"sha1\" | \"sha256\"> | null\n  /**\n   * The custom function (or path to file or module id) to sign Windows executable.\n   */\n  readonly sign?: CustomWindowsSign | string | null\n  /**\n   * The path to the *.pfx certificate you want to sign with. Please use it only if you cannot use env variable `CSC_LINK` (`WIN_CSC_LINK`) for some reason.\n   * Please see [Code Signing](/code-signing).\n   */\n  readonly certificateFile?: string | null\n  /**\n   * The password to the certificate provided in `certificateFile`. Please use it only if you cannot use env variable `CSC_KEY_PASSWORD` (`WIN_CSC_KEY_PASSWORD`) for some reason.\n   * Please see [Code Signing](/code-signing).\n   */\n  readonly certificatePassword?: string | null\n  /**\n   * The name of the subject of the signing certificate, which is often labeled with the field name `issued to`. Required only for EV Code Signing and works only on Windows (or on macOS if [Parallels Desktop](https://www.parallels.com/products/desktop/) Windows 10 virtual machines exits).\n   */\n  readonly certificateSubjectName?: string | null\n  /**\n   * The SHA1 hash of the signing certificate. The SHA1 hash is commonly specified when multiple certificates satisfy the criteria specified by the remaining switches. Works only on Windows (or on macOS if [Parallels Desktop](https://www.parallels.com/products/desktop/) Windows 10 virtual machines exits).\n   */\n  readonly certificateSha1?: string | null\n  /**\n   * The path to an additional certificate file you want to add to the signature block.\n   */\n  readonly additionalCertificateFile?: string | null\n  /**\n   * The URL of the RFC 3161 time stamp server.\n   * @default http://timestamp.digicert.com\n   */\n  readonly rfc3161TimeStampServer?: string | null\n  /**\n   * The URL of the time stamp server.\n   * @default http://timestamp.digicert.com\n   */\n  readonly timeStampServer?: string | null\n\n  /**\n   * [The publisher name](https://github.com/electron-userland/electron-builder/issues/1187#issuecomment-278972073), exactly as in your code signed certificate. Several names can be provided.\n   * Defaults to common name from your code signing certificate.\n   */\n  readonly publisherName?: string | Array<string> | null\n\n  /**\n   * Whether to verify the signature of an available update before installation.\n   * The [publisher name](#publisherName) will be used for the signature verification.\n   *\n   * @default true\n   */\n  readonly verifyUpdateCodeSignature?: boolean\n\n  /**\n   * The [security level](https://msdn.microsoft.com/en-us/library/6ad1fshk.aspx#Anchor_9) at which the application requests to be executed.\n   * Cannot be specified per target, allowed only in the `win`.\n   * @default asInvoker\n   */\n  readonly requestedExecutionLevel?: RequestedExecutionLevel | null\n\n  /**\n   * Whether to sign and add metadata to executable. Advanced option.\n   * @default true\n   */\n  readonly signAndEditExecutable?: boolean\n\n  /**\n   * Whether to sign DLL files. Advanced option.\n   * @see https://github.com/electron-userland/electron-builder/issues/3101#issuecomment-404212384\n   * @default false\n   * @deprecated Use `signExts` instead for more explicit control\n   */\n  readonly signDlls?: boolean\n\n  /**\n   * Explicit file extensions to also sign. Advanced option.\n   * @see https://github.com/electron-userland/electron-builder/issues/7329\n   * @default null\n   */\n  readonly signExts?: string[] | null\n}\n\nexport type RequestedExecutionLevel = \"asInvoker\" | \"highestAvailable\" | \"requireAdministrator\"\n"]}
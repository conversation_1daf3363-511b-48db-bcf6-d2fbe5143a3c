// Fichier : src/renderer/src/pages/LoginPage.jsx

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function LoginPage() {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const navigate = useNavigate();

    const handleLogin = async (e) => {
        e.preventDefault();
        setError('');
        try {
            // On appelle directement l'API exposée par preload.js !
            const user = await window.api.session.authenticate(username, password);
            if (user) {
                // Si la connexion réussit, on navigue vers la racine.
                // Le routeur se chargera d'afficher la bonne page (Index ou Caisse).
                navigate('/');
            } else {
                setError('Nom d\'utilisateur ou mot de passe incorrect.');
            }
        } catch (err) {
            setError('Une erreur est survenue lors de la connexion.');
            console.error(err);
        }
    };

    return (
        <div>
            {/* Ici, tu vas copier/coller le HTML de ton ancien login.html */}
            <h1>Connexion</h1>
            <form onSubmit={handleLogin}>
                <input
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Nom d'utilisateur"
                />
                <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Mot de passe"
                />
                <button type="submit">Se connecter</button>
            </form>
            {error && <p style={{ color: 'red' }}>{error}</p>}
        </div>
    );
}

export default LoginPage;

{"version": 3, "file": "ProtonFramework.js", "sourceRoot": "", "sources": ["../src/ProtonFramework.ts"], "names": [], "mappings": ";;;AACA,+CAAkC;AAClC,+DAAwD;AACxD,iCAAiC;AACjC,uDAAwD;AACxD,gEAA4D;AAC5D,oDAAoD;AAEpD,MAAa,eAAgB,SAAQ,+BAAc;IAMjD,YAAY,OAAe,EAAE,gBAAwB,EAAE,aAAsB;QAC3E,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,aAAa,CAAC,CAAA;QANxC,SAAI,GAAG,QAAQ,CAAA;QAExB,qCAAqC;QAC5B,uBAAkB,GAAG,oBAAoB,CAAA;IAIlD,CAAC;IAED,cAAc,CAAC,QAAkB;QAC/B,IAAI,QAAQ,KAAK,eAAQ,CAAC,OAAO,EAAE,CAAC;YAClC,OAAO,IAAA,6BAAe,EAAC,uCAAuC,CAAC,CAAA;QACjE,CAAC;aAAM,IAAI,QAAQ,KAAK,eAAQ,CAAC,KAAK,EAAE,CAAC;YACvC,OAAO,IAAA,6BAAe,EAAC,2BAA2B,CAAC,CAAA;QACrD,CAAC;aAAM,CAAC;YACN,OAAO,IAAA,6BAAe,EAAC,wCAAwC,CAAC,CAAA;QAClE,CAAC;IACH,CAAC;IAED,iBAAiB;QACf,IAAI,KAAU,CAAA;QACd,MAAM,YAAY,GAAQ,EAAE,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAA;QAC9D,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,KAAK,MAAM,EAAE,CAAC;YACjD,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,CAAA;YAC9B,mEAAmE;YACnE,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QAC1D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;YAC/B,CAAC;YAAC,OAAO,CAAM,EAAE,CAAC;gBAChB,wBAAwB;gBACxB,kBAAG,CAAC,KAAK,CAAC,IAAI,EAAE,yCAAyC,CAAC,CAAA;gBAC1D,OAAO,IAAI,CAAA;YACb,CAAC;QACH,CAAC;QAED,kBAAG,CAAC,IAAI,CACN;YACE,OAAO,EAAE,IAAA,wCAAiB,EAAC,YAAY,EAAE,IAAI,GAAG,CAAS,CAAC,SAAS,CAAC,CAAC,CAAC;SACvE,EACD,mCAAmC,CACpC,CAAA;QACD,OAAO,CAAC,IAAI,EAAuB,EAAE;YACnC,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,sCAAoB,CAAC,EAAE,CAAC;gBAC5F,OAAO,IAAI,CAAA;YACb,CAAC;YAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,OAAO,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,KAAY,EAAE,MAAW,EAAE,EAAE;oBAC3E,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;wBAClB,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;oBACtB,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,CAAA;oBACf,CAAC;gBACH,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC,CAAA;IACH,CAAC;CACF;AA3DD,0CA2DC;AAED,SAAS,aAAa,CAAC,KAAU,EAAE,YAAiB,EAAE,WAAmB;IACvE,uHAAuH;IACvH,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAA;IACnI,YAAY,CAAC,OAAO,GAAG;QACrB,UAAU;QACV,OAAO,CAAC,sCAAsC,CAAC,CAAC,OAAO;QAEvD,UAAU;QACV,OAAO,CAAC,4CAA4C,CAAC,CAAC,OAAO;QAC7D,OAAO,CAAC,qDAAqD,CAAC,CAAC,OAAO;QACtE,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;QAC/E,CAAC,OAAO,CAAC,0CAA0C,CAAC,CAAC,OAAO,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;QACtF,CAAC,OAAO,CAAC,oDAAoD,CAAC,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;QACzF,OAAO,CAAC,uCAAuC,CAAC,CAAC,OAAO;QAExD,UAAU;QACV,CAAC,OAAO,CAAC,mCAAmC,CAAC,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;QACxE,OAAO,CAAC,sCAAsC,CAAC,CAAC,OAAO;QACvD,OAAO,CAAC,8CAA8C,CAAC,CAAC,OAAO;QAC/D,OAAO,CAAC,0CAA0C,CAAC,CAAC,OAAO;QAC3D,OAAO,CAAC,0CAA0C,CAAC,CAAC,OAAO;QAE3D,UAAU;QACV,OAAO,CAAC,qCAAqC,CAAC,CAAC,OAAO;QACtD,OAAO,CAAC,kCAAkC,CAAC,CAAC,OAAO;QACnD,CAAC,OAAO,CAAC,yCAAyC,CAAC,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;QAC9E,OAAO,CAAC,qCAAqC,CAAC,CAAC,OAAO;KACvD,CAAA;IACD,YAAY,CAAC,OAAO,GAAG,KAAK,CAAA;IAC5B,OAAO,KAAK,CAAA;AACd,CAAC", "sourcesContent": ["import { FileTransformer } from \"builder-util/out/fs\"\nimport { log } from \"builder-util\"\nimport { safeStringifyJson } from \"builder-util-runtime\"\nimport { Platform } from \"./core\"\nimport { NODE_MODULES_PATTERN } from \"./fileTransformer\"\nimport { LibUiFramework } from \"./frameworks/LibUiFramework\"\nimport { getTemplatePath } from \"./util/pathManager\"\n\nexport class ProtonFramework extends LibUiFramework {\n  readonly name = \"proton\"\n\n  // noinspection JSUnusedGlobalSymbols\n  readonly defaultAppIdPrefix = \"com.proton-native.\"\n\n  constructor(version: string, distMacOsAppName: string, isUseLaunchUi: boolean) {\n    super(version, distMacOsAppName, isUseLaunchUi)\n  }\n\n  getDefaultIcon(platform: Platform): string {\n    if (platform === Platform.WINDOWS) {\n      return getTemplatePath(\"icons/proton-native/proton-native.ico\")\n    } else if (platform === Platform.LINUX) {\n      return getTemplatePath(\"icons/proton-native/linux\")\n    } else {\n      return getTemplatePath(\"icons/proton-native/proton-native.icns\")\n    }\n  }\n\n  createTransformer(): FileTransformer | null {\n    let babel: any\n    const babelOptions: any = { ast: false, sourceMaps: \"inline\" }\n    if (process.env.TEST_SET_BABEL_PRESET === \"true\") {\n      babel = require(\"@babel/core\")\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      babel = testOnlyBabel(babel, babelOptions, this.version)\n    } else {\n      try {\n        babel = require(\"babel-core\")\n      } catch (e: any) {\n        // babel isn't installed\n        log.debug(null, \"don't transpile source code using Babel\")\n        return null\n      }\n    }\n\n    log.info(\n      {\n        options: safeStringifyJson(babelOptions, new Set<string>([\"presets\"])),\n      },\n      \"transpile source code using Babel\"\n    )\n    return (file): Promise<any> | null => {\n      if (!(file.endsWith(\".js\") || file.endsWith(\".jsx\")) || file.includes(NODE_MODULES_PATTERN)) {\n        return null\n      }\n\n      return new Promise((resolve, reject) => {\n        return babel.transformFile(file, babelOptions, (error: Error, result: any) => {\n          if (error == null) {\n            resolve(result.code)\n          } else {\n            reject(error)\n          }\n        })\n      })\n    }\n  }\n}\n\nfunction testOnlyBabel(babel: any, babelOptions: any, nodeVersion: string): any {\n  // out test dir can be located outside of electron-builder node_modules and babel cannot resolve string names of preset\n  babelOptions.presets = [[require(\"@babel/preset-env\").default, { targets: { node: nodeVersion } }], require(\"@babel/preset-react\")]\n  babelOptions.plugins = [\n    // stage 0\n    require(\"@babel/plugin-proposal-function-bind\").default,\n\n    // stage 1\n    require(\"@babel/plugin-proposal-export-default-from\").default,\n    require(\"@babel/plugin-proposal-logical-assignment-operators\").default,\n    [require(\"@babel/plugin-proposal-optional-chaining\").default, { loose: false }],\n    [require(\"@babel/plugin-proposal-pipeline-operator\").default, { proposal: \"minimal\" }],\n    [require(\"@babel/plugin-proposal-nullish-coalescing-operator\").default, { loose: false }],\n    require(\"@babel/plugin-proposal-do-expressions\").default,\n\n    // stage 2\n    [require(\"@babel/plugin-proposal-decorators\").default, { legacy: true }],\n    require(\"@babel/plugin-proposal-function-sent\").default,\n    require(\"@babel/plugin-proposal-export-namespace-from\").default,\n    require(\"@babel/plugin-proposal-numeric-separator\").default,\n    require(\"@babel/plugin-proposal-throw-expressions\").default,\n\n    // stage 3\n    require(\"@babel/plugin-syntax-dynamic-import\").default,\n    require(\"@babel/plugin-syntax-import-meta\").default,\n    [require(\"@babel/plugin-proposal-class-properties\").default, { loose: false }],\n    require(\"@babel/plugin-proposal-json-strings\").default,\n  ]\n  babelOptions.babelrc = false\n  return babel\n}\n"]}
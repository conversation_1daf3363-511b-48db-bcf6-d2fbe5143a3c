// Fichier : src/renderer/src/pages/ActivationPage.jsx

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function ActivationPage() {
    const [licenseKey, setLicenseKey] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const handleActivation = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);
        
        try {
            // Appel de l'API d'activation via preload.js
            const result = await window.api.license.activate(licenseKey);
            if (result && result.success) {
                // Activation réussie, redirection vers la page de login
                navigate('/login');
            } else {
                setError(result?.message || 'Erreur lors de l\'activation de la licence.');
            }
        } catch (err) {
            setError('Une erreur est survenue lors de l\'activation.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div>
            <h1>Activation de la Licence</h1>
            <p>Veuillez entrer votre clé de licence pour activer GestionPro.</p>
            
            <form onSubmit={handleActivation}>
                <input
                    type="text"
                    value={licenseKey}
                    onChange={(e) => setLicenseKey(e.target.value)}
                    placeholder="Entrez votre clé de licence"
                    disabled={loading}
                />
                <button type="submit" disabled={loading || !licenseKey.trim()}>
                    {loading ? 'Activation en cours...' : 'Activer'}
                </button>
            </form>
            
            {error && <p style={{ color: 'red' }}>{error}</p>}
            
            <div>
                <h3>Informations</h3>
                <p>• Votre clé de licence vous a été fournie lors de l'achat</p>
                <p>• Une connexion Internet est requise pour l'activation</p>
                <p>• Contactez le support si vous rencontrez des difficultés</p>
            </div>
        </div>
    );
}

export default ActivationPage;

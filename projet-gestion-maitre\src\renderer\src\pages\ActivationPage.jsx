// Fichier : src/renderer/src/pages/ActivationPage.jsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

function ActivationPage() {
    const [licenseKey, setLicenseKey] = useState('');
    const [machineId, setMachineId] = useState('');
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    useEffect(() => {
        // Récupérer l'ID de la machine au chargement
        window.api.app.getMachineId().then(setMachineId);
    }, []);

    const handleActivation = async (e) => {
        e.preventDefault();
        setError('');
        setLoading(true);
        try {
            const result = await window.api.app.activateLicense(licenseKey);
            if (result.success) {
                // Si l'activation réussit, on navigue vers la page de login
                navigate('/login');
            } else {
                setError(result.message || 'La clé de licence est invalide.');
            }
        } catch (err) {
            setError('Une erreur critique est survenue.');
            console.error(err);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div>
            {/* Ici, tu copieras le HTML de ton ancien activation.html */}
            <h1>Activation du produit</h1>
            <p>Votre ID Machine : {machineId}</p>
            <form onSubmit={handleActivation}>
                <input
                    type="text"
                    value={licenseKey}
                    onChange={(e) => setLicenseKey(e.target.value)}
                    placeholder="Entrez votre clé de licence"
                    disabled={loading}
                />
                <button type="submit" disabled={loading}>
                    {loading ? 'Activation en cours...' : 'Activer'}
                </button>
            </form>
            {error && <p style={{ color: 'red' }}>{error}</p>}
        </div>
    );
}

export default ActivationPage;

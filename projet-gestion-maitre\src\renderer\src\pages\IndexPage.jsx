// Fichier : src/renderer/src/pages/IndexPage.jsx
import React, { useState, useEffect } from 'react';

function IndexPage() {
    const [products, setProducts] = useState([]);
    const [clients, setClients] = useState([]);
    const [salesHistory, setSalesHistory] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        // Cette fonction s'exécute une seule fois au chargement de la page.
        const fetchAllData = async () => {
            try {
                // On utilise notre API pour récupérer toutes les données
                const [allProducts, allClients, recentSales] = await Promise.all([
                    window.api.products.getAll(),
                    window.api.clients.getAll(),
                    window.api.sales.getHistory({ limit: 10 }) // Les 10 dernières ventes
                ]);

                setProducts(allProducts);
                setClients(allClients);
                setSalesHistory(recentSales);
            } catch (err) {
                console.error("Erreur lors de la récupération des données:", err);
                setError('Impossible de charger les données.');
            } finally {
                setLoading(false);
            }
        };

        fetchAllData();
    }, []); // Le tableau vide [] assure que cela ne s'exécute qu'une fois.

    if (loading) {
        return <div>Chargement des produits...</div>;
    }

    if (error) {
        return <div style={{ color: 'red' }}>{error}</div>;
    }

    return (
        <div>
            {/* Vous copierez ici le HTML de votre ancien index.html */}
            <h1>Tableau de bord du Propriétaire</h1>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))', gap: '20px' }}>

                {/* Section Produits */}
                <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                    <h2>📦 Liste des Produits ({products.length})</h2>
                    <ul style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        {products.length > 0 ? (
                            products.map(product => (
                                <li key={product.id} style={{ marginBottom: '8px' }}>
                                    <strong>{product.name}</strong> - {product.price} MAD
                                    <span style={{ color: product.stock < 10 ? 'red' : 'green' }}>
                                        (Stock: {product.stock})
                                    </span>
                                </li>
                            ))
                        ) : (
                            <li>Aucun produit trouvé.</li>
                        )}
                    </ul>
                </div>

                {/* Section Clients */}
                <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                    <h2>👥 Liste des Clients ({clients.length})</h2>
                    <ul style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        {clients.length > 0 ? (
                            clients.map(client => (
                                <li key={client.id} style={{ marginBottom: '8px' }}>
                                    <strong>{client.name}</strong>
                                    {client.phone && <span> - {client.phone}</span>}
                                    {client.credit > 0 && (
                                        <span style={{ color: 'orange', fontWeight: 'bold' }}>
                                            (Crédit: {client.credit} MAD)
                                        </span>
                                    )}
                                </li>
                            ))
                        ) : (
                            <li>Aucun client trouvé.</li>
                        )}
                    </ul>
                </div>

                {/* Section Historique des Ventes */}
                <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px', gridColumn: 'span 2' }}>
                    <h2>📊 Historique des Ventes Récentes ({salesHistory.length})</h2>
                    <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        {salesHistory.length > 0 ? (
                            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                <thead>
                                    <tr style={{ backgroundColor: '#f5f5f5' }}>
                                        <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Date</th>
                                        <th style={{ padding: '8px', textAlign: 'left', border: '1px solid #ddd' }}>Client</th>
                                        <th style={{ padding: '8px', textAlign: 'right', border: '1px solid #ddd' }}>Total</th>
                                        <th style={{ padding: '8px', textAlign: 'center', border: '1px solid #ddd' }}>Paiement</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {salesHistory.map(sale => (
                                        <tr key={sale.id}>
                                            <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                                                {new Date(sale.date).toLocaleDateString('fr-FR')}
                                            </td>
                                            <td style={{ padding: '8px', border: '1px solid #ddd' }}>
                                                {sale.client_name || 'Client anonyme'}
                                            </td>
                                            <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'right', fontWeight: 'bold' }}>
                                                {sale.total} MAD
                                            </td>
                                            <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center' }}>
                                                <span style={{
                                                    padding: '4px 8px',
                                                    borderRadius: '4px',
                                                    backgroundColor: sale.payment_method === 'Comptant' ? '#d4edda' : '#fff3cd',
                                                    color: sale.payment_method === 'Comptant' ? '#155724' : '#856404'
                                                }}>
                                                    {sale.payment_method}
                                                </span>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        ) : (
                            <p>Aucune vente trouvée.</p>
                        )}
                    </div>
                </div>

            </div>
        </div>
    );
}

export default IndexPage;

// Fichier : src/renderer/src/pages/IndexPage.jsx
import React, { useState, useEffect } from 'react';

function IndexPage() {
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');

    useEffect(() => {
        // Cette fonction s'exécute une seule fois au chargement de la page.
        const fetchProducts = async () => {
            try {
                // On utilise notre API pour récupérer les produits
                const allProducts = await window.api.products.getAll();
                setProducts(allProducts);
            } catch (err) {
                console.error("Erreur lors de la récupération des produits:", err);
                setError('Impossible de charger les produits.');
            } finally {
                setLoading(false);
            }
        };

        fetchProducts();
    }, []); // Le tableau vide [] assure que cela ne s'exécute qu'une fois.

    if (loading) {
        return <div>Chargement des produits...</div>;
    }

    if (error) {
        return <div style={{ color: 'red' }}>{error}</div>;
    }

    return (
        <div>
            {/* Vous copierez ici le HTML de votre ancien index.html */}
            <h1>Tableau de bord du Propriétaire</h1>

            <h2>Liste des Produits</h2>
            <ul>
                {products.length > 0 ? (
                    products.map(product => (
                        <li key={product.id}>
                            {product.name} - {product.price} € (Stock: {product.stock})
                        </li>
                    ))
                ) : (
                    <li>Aucun produit trouvé.</li>
                )}
            </ul>
            {/* Ici, vous continuerez en ajoutant les clients, les ventes, etc. */}
        </div>
    );
}

export default IndexPage;

// Fichier : src/renderer/src/pages/IndexPage.jsx

import React, { useState, useEffect } from 'react';

function IndexPage() {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Récupérer les informations de l'utilisateur connecté
        window.api.session.getCurrentUser().then(currentUser => {
            setUser(currentUser);
            setLoading(false);
        });
    }, []);

    const handleLogout = () => {
        // Ici tu pourras ajouter la logique de déconnexion
        // Pour l'instant, on recharge juste la page
        window.location.reload();
    };

    if (loading) {
        return <div>Chargement du dashboard...</div>;
    }

    return (
        <div>
            <header>
                <h1>🏪 GestionPro - Dashboard Propriétaire</h1>
                <div>
                    <span>Bienvenue, {user?.username}</span>
                    <button onClick={handleLogout}>Déconnexion</button>
                </div>
            </header>

            <main>
                <div>
                    <h2>📊 Vue d'ensemble</h2>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
                        
                        <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                            <h3>💰 Caisse</h3>
                            <p>Point de vente et gestion des transactions</p>
                            <button>Ouvrir la Caisse</button>
                        </div>

                        <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                            <h3>📦 Produits & Stock</h3>
                            <p>Gestion du catalogue et des stocks</p>
                            <button>Gérer les Produits</button>
                        </div>

                        <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                            <h3>👥 Clients</h3>
                            <p>Base de données clients et crédits</p>
                            <button>Gérer les Clients</button>
                        </div>

                        <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                            <h3>📊 Historique</h3>
                            <p>Historique des ventes et rapports</p>
                            <button>Voir l'Historique</button>
                        </div>

                        <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                            <h3>🧾 Facturation</h3>
                            <p>Système de facturation avec TVA</p>
                            <button>Créer une Facture</button>
                        </div>

                        <div style={{ border: '1px solid #ccc', padding: '20px', borderRadius: '8px' }}>
                            <h3>⚙️ Paramètres</h3>
                            <p>Configuration de l'application</p>
                            <button>Paramètres</button>
                        </div>

                    </div>
                </div>

                <div style={{ marginTop: '40px' }}>
                    <h2>📈 Statistiques Rapides</h2>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                        <div style={{ backgroundColor: '#f0f8ff', padding: '15px', borderRadius: '8px' }}>
                            <h4>Ventes du Jour</h4>
                            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>0 MAD</p>
                        </div>
                        <div style={{ backgroundColor: '#f0fff0', padding: '15px', borderRadius: '8px' }}>
                            <h4>Produits en Stock</h4>
                            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>0</p>
                        </div>
                        <div style={{ backgroundColor: '#fff8f0', padding: '15px', borderRadius: '8px' }}>
                            <h4>Clients Actifs</h4>
                            <p style={{ fontSize: '24px', fontWeight: 'bold' }}>0</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
}

export default IndexPage;

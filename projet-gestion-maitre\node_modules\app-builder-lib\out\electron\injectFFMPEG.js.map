{"version": 3, "file": "injectFFMPEG.js", "sourceRoot": "", "sources": ["../../src/electron/injectFFMPEG.ts"], "names": [], "mappings": ";;AAAA,yBAAwB;AACxB,6BAA4B;AAG5B,+CAAkC;AAClC,gDAAuC;AAGvC,qJAAqJ;AACrJ,MAAM,cAAc,GAAG,KAAK,EAAE,eAAuB,EAAE,QAA8B,EAAE,IAAY,EAAE,EAAE;IACrG,MAAM,cAAc,GAAG,WAAW,eAAe,IAAI,QAAQ,IAAI,IAAI,MAAM,CAAA;IAC3E,MAAM,GAAG,GAAG,2DAA2D,eAAe,IAAI,cAAc,EAAE,CAAA;IAE1G,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,EAAE,oCAAoC,CAAC,CAAA;IACxE,OAAO,IAAA,oBAAM,EAAC,cAAc,EAAE,GAAG,CAAC,CAAA;AACpC,CAAC,CAAA;AAED,MAAM,UAAU,GAAG,CAAC,UAAkB,EAAE,QAA8B,EAAE,EAAE,CAAC,CAAC,UAAkB,EAAE,EAAE;IAChG,IAAI,QAAQ,GAAG,YAAY,CAAA;IAC3B,IAAI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;QACzC,QAAQ,GAAG,iBAAiB,CAAA;IAC9B,CAAC;SAAM,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;QAChC,QAAQ,GAAG,cAAc,CAAA;IAC3B,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IAClD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;IACxD,kBAAG,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,gCAAgC,CAAC,CAAA;IAEnF,gDAAgD;IAChD,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,+CAA+C,OAAO,EAAE,CAAC,CAAA;IAC3E,CAAC;IAED,0DAA0D;IAC1D,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC;QAC9B,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;IACzC,CAAC;IACD,OAAO,aAAa,CAAA;AACtB,CAAC,CAAA;AAED,SAAwB,YAAY,CAAC,OAAgD,EAAE,gBAAwB;IAC7G,IAAI,OAAO,GAAG,OAAO,CAAC,SAAS,CAAA;IAC/B,IAAI,OAAO,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;QACtC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,oFAAoF,CAAC,CAAA;IACjI,CAAC;IAED,OAAO,cAAc,CAAC,gBAAgB,EAAE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC,CAAA;AAC7H,CAAC;AAPD,+BAOC", "sourcesContent": ["import * as fs from \"fs\"\nimport * as path from \"path\"\nimport { ElectronPlatformName } from \"./ElectronFramework\"\n\nimport { log } from \"builder-util\"\nimport { getBin } from \"../binDownload\"\nimport { PrepareApplicationStageDirectoryOptions } from \"../Framework\"\n\n// NOTE: Adapted from https://github.com/MarshallOfSound/electron-packager-plugin-non-proprietary-codecs-ffmpeg to resolve dependency vulnerabilities\nconst downloadFFMPEG = async (electronVersion: string, platform: ElectronPlatformName, arch: string) => {\n  const ffmpegFileName = `ffmpeg-v${electronVersion}-${platform}-${arch}.zip`\n  const url = `https://github.com/electron/electron/releases/download/v${electronVersion}/${ffmpegFileName}`\n\n  log.info({ file: ffmpegFileName }, \"downloading non-proprietary FFMPEG\")\n  return getBin(ffmpegFileName, url)\n}\n\nconst copyFFMPEG = (targetPath: string, platform: ElectronPlatformName) => (sourcePath: string) => {\n  let fileName = \"ffmpeg.dll\"\n  if ([\"darwin\", \"mas\"].includes(platform)) {\n    fileName = \"libffmpeg.dylib\"\n  } else if (platform === \"linux\") {\n    fileName = \"libffmpeg.so\"\n  }\n\n  const libPath = path.resolve(sourcePath, fileName)\n  const libTargetPath = path.resolve(targetPath, fileName)\n  log.info({ lib: libPath, target: libTargetPath }, \"copying non-proprietary FFMPEG\")\n\n  // If the source doesn't exist we have a problem\n  if (!fs.existsSync(libPath)) {\n    throw new Error(`Failed to find FFMPEG library file at path: ${libPath}`)\n  }\n\n  // If we are copying to the source we can stop immediately\n  if (libPath !== libTargetPath) {\n    fs.copyFileSync(libPath, libTargetPath)\n  }\n  return libTargetPath\n}\n\nexport default function injectFFMPEG(options: PrepareApplicationStageDirectoryOptions, electrionVersion: string) {\n  let libPath = options.appOutDir\n  if (options.platformName === \"darwin\") {\n    libPath = path.resolve(options.appOutDir, \"Electron.app/Contents/Frameworks/Electron Framework.framework/Versions/A/Libraries\")\n  }\n\n  return downloadFFMPEG(electrionVersion, options.platformName, options.arch).then(copyFFMPEG(libPath, options.platformName))\n}\n"]}
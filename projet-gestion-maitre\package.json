{"name": "projet-gestion-maitre", "version": "2.0.0", "description": "Application de bureau Maître pour la gestion complète.", "main": "main.js", "scripts": {"start": "electron .", "watch:css": "tailwindcss -i ./src/css/input.css -o ./src/css/output.css --watch", "dev:react": "vite src/renderer", "dev:electron": "nodemon --exec \"electron .\"", "dev": "concurrently \"npm:watch:css\" \"npm:dev:react\" \"npm:dev:electron\"", "build:react": "vite build src/renderer", "dist": "npm run build:react && electron-builder", "rebuild": "electron-rebuild", "fix-modules": "npm rebuild && electron-rebuild", "test-tva": "node test-tva-system.js", "demo-tva": "node demo-tva-system.js", "validate-build": "node validate-build.js", "verify-installer": "node verify-installer.js", "build-complete": "npm run validate-build && npm run dist && npm run verify-installer", "postinstall": "electron-rebuild"}, "build": {"appId": "com.gestionpro.app", "productName": "GestionPro", "copyright": "Copyright © 2025 GestionPro", "directories": {"output": "gestionpro-installer-final", "buildResources": "build"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "extraResources": [{"from": "database", "to": "database", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "artifactName": "${productName} Setup ${version}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "GestionPro"}, "publish": null}, "nodemonConfig": {"ignore": ["database/*", "src/css/output.css"], "delay": 1500}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcrypt": "^5.1.1", "better-sqlite3": "^9.4.3", "node-fetch": "^3.3.2", "node-machine-id": "^1.1.12"}, "devDependencies": {"concurrently": "^8.2.2", "electron": "^28.3.3", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "nodemon": "^3.1.4", "tailwindcss": "^3.4.4"}}
export * from '../index';
import * as _operators from '../operators/index';
export const operators = _operators;
import * as _testing from '../testing/index';
export const testing = _testing;
import * as _ajax from '../ajax/index';
export const ajax = _ajax;
import * as _webSocket from '../webSocket/index';
export const webSocket = _webSocket;
import * as _fetch from '../fetch/index';
export const fetch = _fetch;
//# sourceMappingURL=umd.js.map
{"version": 3, "file": "readBufferedLines.js", "sourceRoot": "", "sources": ["../../../../../src/fileCompareHandler/lines/lineReader/readBufferedLines.ts"], "names": [], "mappings": ";;;AAGA,MAAM,qBAAqB,GAAG,eAAe,CAAA;AAE7C;;;;;;;;;GASG;AACH,SAAgB,iBAAiB,CAAC,GAAW,EAAE,IAAY,EAAE,mBAA2B,EAAE,IAAY,EAAE,SAAmB;IACvH,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QACjC,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAA;KAC/D;IACD,IAAI,IAAI,KAAK,CAAC,EAAE;QACZ,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAA;KACrE;IAED,MAAM,WAAW,GAAG,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAA;IACxD,MAAM,KAAK,GAAG,CAAC,GAAG,SAAS,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,qBAAqB,CAAa,CAAC,CAAA;IAErF,MAAM,UAAU,GAAG,IAAI,GAAG,mBAAmB,CAAA;IAC7C,IAAI,UAAU,EAAE;QACZ,OAAO;YACH,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,IAAI;SACpC,CAAA;KACJ;IAED,OAAO,cAAc,CAAC,KAAK,CAAC,CAAA;AAChC,CAAC;AAnBD,8CAmBC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CAAC,KAAe;IACnC,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;IACxC,OAAO;QACH,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACvC,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,KAAK;KACpB,CAAA;AACL,CAAC"}
import { createRequire as ___createRequire } from 'module'; const require = ___createRequire(import.meta.url);
import { BuildEnvironment, build, buildEnvironmentOptionsDefaults, builderOptionsDefaults, createBuilder, createToImportMetaURLBasedRelativeRuntime, injectEnvironmentToHooks, onRollupLog, resolveBuildEnvironmentOptions, resolveBuildOutputs, resolveBuildPlugins, resolveBuilderOptions, resolveLibFilename, resolveUserExternal, toOutputFilePathInCss, toOutputFilePathInHtml, toOutputFilePathInJS, toOutputFilePathWithoutRuntime } from "./dep-BHkUv4Z8.js";
import "./dep-Ctugieod.js";

export { createBuilder, resolveBuildPlugins };
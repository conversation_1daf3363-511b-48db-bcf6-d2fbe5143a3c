import { File } from "../index";
import { type TokenType } from "../tokenizer/types";
export declare function parseTopLevel(): File;
export declare function parseStatement(declaration: boolean): void;
export declare function parseDecorators(): void;
export declare function baseParseMaybeDecoratorArguments(): void;
export declare function parseVarStatement(isBlockScope: boolean): void;
export declare function parseBlock(isFunctionScope?: boolean, contextId?: number): void;
export declare function parseBlockBody(end: TokenType): void;
export declare function parseFunction(functionStart: number, isStatement: boolean, optionalId?: boolean): void;
export declare function parseFunctionParams(allowModifiers?: boolean, funcContextId?: number): void;
export declare function parseClass(isStatement: boolean, optionalId?: boolean): void;
export declare function parseClassPropertyName(classContextId: number): void;
export declare function parsePostMemberNameModifiers(): void;
export declare function parseClassProperty(): void;
export declare function parseExport(): void;
export declare function parseExportFrom(): void;
export declare function baseParseExportStar(): void;
export declare function parseExportSpecifiers(): void;
export declare function parseImport(): void;

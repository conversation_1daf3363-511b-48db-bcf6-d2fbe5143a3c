{"version": 3, "file": "macroExpander.js", "sourceRoot": "", "sources": ["../../src/util/macroExpander.ts"], "names": [], "mappings": ";;;AAAA,+CAAwD;AAGxD,SAAgB,WAAW,CAAC,OAAe,EAAE,IAA+B,EAAE,OAAgB,EAAE,QAAa,EAAE,EAAE,sBAAsB,GAAG,IAAI;IAC5I,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,OAAO,GAAG,OAAO;YACf,uDAAuD;aACtD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;YACxB,uDAAuD;aACtD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;YACxB,uDAAuD;aACtD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;YACxB,uDAAuD;aACtD,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;IAC5B,CAAC;IAED,OAAO,OAAO,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC,KAAK,EAAE,EAAE,EAAU,EAAE;QACpE,QAAQ,EAAE,EAAE,CAAC;YACX,KAAK,aAAa;gBAChB,OAAO,sBAAsB,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAA;YAEpF,KAAK,MAAM;gBACT,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,wCAAwC;oBACxC,OAAO,EAAE,CAAA;gBACX,CAAC;gBACD,OAAO,IAAI,CAAA;YAEb,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;gBACvC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;oBACxB,MAAM,IAAI,wCAAyB,CAAC,0BAA0B,OAAO,4BAA4B,EAAE,yCAAyC,CAAC,CAAA;gBAC/I,CAAC;gBACD,OAAO,WAAW,CAAA;YACpB,CAAC;YAED,KAAK,UAAU;gBACb,OAAO,OAAO,CAAC,QAAQ,CAAA;YAEzB,KAAK,SAAS;gBACZ,OAAO,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAA;YAEpC,OAAO,CAAC,CAAC,CAAC;gBACR,IAAI,EAAE,IAAI,OAAO,EAAE,CAAC;oBAClB,OAAQ,OAAe,CAAC,EAAE,CAAC,CAAA;gBAC7B,CAAC;gBAED,IAAI,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,MAAM,OAAO,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;oBAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;oBACrC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;wBACrB,MAAM,IAAI,wCAAyB,CAAC,0BAA0B,OAAO,UAAU,OAAO,iBAAiB,EAAE,sCAAsC,CAAC,CAAA;oBAClJ,CAAC;oBACD,OAAO,QAAQ,CAAA;gBACjB,CAAC;gBAED,MAAM,KAAK,GAAG,KAAK,CAAC,EAAE,CAAC,CAAA;gBACvB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;oBAClB,MAAM,IAAI,wCAAyB,CAAC,0BAA0B,OAAO,YAAY,EAAE,iBAAiB,EAAE,wCAAwC,CAAC,CAAA;gBACjJ,CAAC;qBAAM,CAAC;oBACN,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AA9DD,kCA8DC", "sourcesContent": ["import { InvalidConfigurationError } from \"builder-util\"\nimport { AppInfo } from \"../appInfo\"\n\nexport function expandMacro(pattern: string, arch: string | null | undefined, appInfo: AppInfo, extra: any = {}, isProductNameSanitized = true): string {\n  if (arch == null) {\n    pattern = pattern\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\"-${arch}\", \"\")\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\" ${arch}\", \"\")\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\"_${arch}\", \"\")\n      // tslint:disable-next-line:no-invalid-template-strings\n      .replace(\"/${arch}\", \"\")\n  }\n\n  return pattern.replace(/\\${([_a-zA-Z./*+]+)}/g, (match, p1): string => {\n    switch (p1) {\n      case \"productName\":\n        return isProductNameSanitized ? appInfo.sanitizedProductName : appInfo.productName\n\n      case \"arch\":\n        if (arch == null) {\n          // see above, we remove macro if no arch\n          return \"\"\n        }\n        return arch\n\n      case \"author\": {\n        const companyName = appInfo.companyName\n        if (companyName == null) {\n          throw new InvalidConfigurationError(`cannot expand pattern \"${pattern}\": author is not specified`, \"ERR_ELECTRON_BUILDER_AUTHOR_UNSPECIFIED\")\n        }\n        return companyName\n      }\n\n      case \"platform\":\n        return process.platform\n\n      case \"channel\":\n        return appInfo.channel || \"latest\"\n\n      default: {\n        if (p1 in appInfo) {\n          return (appInfo as any)[p1]\n        }\n\n        if (p1.startsWith(\"env.\")) {\n          const envName = p1.substring(\"env.\".length)\n          const envValue = process.env[envName]\n          if (envValue == null) {\n            throw new InvalidConfigurationError(`cannot expand pattern \"${pattern}\": env ${envName} is not defined`, \"ERR_ELECTRON_BUILDER_ENV_NOT_DEFINED\")\n          }\n          return envValue\n        }\n\n        const value = extra[p1]\n        if (value == null) {\n          throw new InvalidConfigurationError(`cannot expand pattern \"${pattern}\": macro ${p1} is not defined`, \"ERR_ELECTRON_BUILDER_MACRO_NOT_DEFINED\")\n        } else {\n          return value\n        }\n      }\n    }\n  })\n}\n"]}
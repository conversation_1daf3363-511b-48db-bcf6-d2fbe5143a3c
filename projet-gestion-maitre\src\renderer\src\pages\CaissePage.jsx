// Fichier : src/renderer/src/pages/CaissePage.jsx
import React from 'react';

function CaissePage() {
    // Vous utiliserez ici useState et useEffect pour charger les produits,
    // gérer le panier, etc., exactement comme pour la page Index.

    return (
        <div>
            {/* Vous copierez ici le HTML de votre ancien caisse.html */}
            <h1>Interface de Caisse</h1>

            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div style={{ width: '60%' }}>
                    <h2>Produits</h2>
                    {/* Ici viendra la liste des produits cliquables */}
                    <p>Chargement des produits...</p>
                </div>
                <div style={{ width: '35%' }}>
                    <h2>Panier</h2>
                    {/* Ici viendra le résumé de la vente en cours */}
                    <p>Le panier est vide.</p>
                </div>
            </div>
        </div>
    );
}

export default CaissePage;

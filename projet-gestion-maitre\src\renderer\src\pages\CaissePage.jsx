// Fichier : src/renderer/src/pages/CaissePage.jsx

import React, { useState, useEffect } from 'react';

function CaissePage() {
    const [user, setUser] = useState(null);
    const [cart, setCart] = useState([]);
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // Récupérer les informations de l'utilisateur connecté
        window.api.session.getCurrentUser().then(currentUser => {
            setUser(currentUser);
            setLoading(false);
        });
    }, []);

    useEffect(() => {
        // Calculer le total du panier
        const newTotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        setTotal(newTotal);
    }, [cart]);

    const handleLogout = () => {
        // Ici tu pourras ajouter la logique de déconnexion
        window.location.reload();
    };

    const addToCart = (product) => {
        setCart(prevCart => {
            const existingItem = prevCart.find(item => item.id === product.id);
            if (existingItem) {
                return prevCart.map(item =>
                    item.id === product.id
                        ? { ...item, quantity: item.quantity + 1 }
                        : item
                );
            } else {
                return [...prevCart, { ...product, quantity: 1 }];
            }
        });
    };

    const removeFromCart = (productId) => {
        setCart(prevCart => prevCart.filter(item => item.id !== productId));
    };

    const updateQuantity = (productId, newQuantity) => {
        if (newQuantity <= 0) {
            removeFromCart(productId);
        } else {
            setCart(prevCart =>
                prevCart.map(item =>
                    item.id === productId
                        ? { ...item, quantity: newQuantity }
                        : item
                )
            );
        }
    };

    const handleCheckout = () => {
        // Ici tu ajouteras la logique de finalisation de vente
        alert('Fonctionnalité de vente à implémenter');
    };

    if (loading) {
        return <div>Chargement de la caisse...</div>;
    }

    return (
        <div style={{ display: 'flex', height: '100vh' }}>
            {/* Sidebar - Panier */}
            <div style={{ width: '400px', borderRight: '1px solid #ccc', padding: '20px' }}>
                <header style={{ marginBottom: '20px' }}>
                    <h2>🛒 Caisse</h2>
                    <div>
                        <span>Vendeur: {user?.username}</span>
                        <button onClick={handleLogout} style={{ marginLeft: '10px' }}>Déconnexion</button>
                    </div>
                </header>

                <div>
                    <h3>Panier</h3>
                    {cart.length === 0 ? (
                        <p>Panier vide</p>
                    ) : (
                        <div>
                            {cart.map(item => (
                                <div key={item.id} style={{ 
                                    display: 'flex', 
                                    justifyContent: 'space-between', 
                                    alignItems: 'center',
                                    padding: '10px',
                                    borderBottom: '1px solid #eee'
                                }}>
                                    <div>
                                        <div>{item.name}</div>
                                        <div style={{ fontSize: '12px', color: '#666' }}>
                                            {item.price} MAD × {item.quantity}
                                        </div>
                                    </div>
                                    <div>
                                        <button onClick={() => updateQuantity(item.id, item.quantity - 1)}>-</button>
                                        <span style={{ margin: '0 10px' }}>{item.quantity}</span>
                                        <button onClick={() => updateQuantity(item.id, item.quantity + 1)}>+</button>
                                        <button 
                                            onClick={() => removeFromCart(item.id)}
                                            style={{ marginLeft: '10px', color: 'red' }}
                                        >
                                            ×
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>

                <div style={{ marginTop: '20px', padding: '20px', backgroundColor: '#f5f5f5' }}>
                    <h3>Total: {total.toFixed(2)} MAD</h3>
                    <button 
                        onClick={handleCheckout}
                        disabled={cart.length === 0}
                        style={{ 
                            width: '100%', 
                            padding: '15px', 
                            backgroundColor: cart.length > 0 ? '#4CAF50' : '#ccc',
                            color: 'white',
                            border: 'none',
                            borderRadius: '5px',
                            fontSize: '16px'
                        }}
                    >
                        Finaliser la Vente
                    </button>
                </div>
            </div>

            {/* Main Content - Produits */}
            <div style={{ flex: 1, padding: '20px' }}>
                <h3>Produits</h3>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))', gap: '15px' }}>
                    {/* Exemples de produits - à remplacer par de vraies données */}
                    {[
                        { id: 1, name: 'Produit 1', price: 10.50 },
                        { id: 2, name: 'Produit 2', price: 25.00 },
                        { id: 3, name: 'Produit 3', price: 15.75 },
                    ].map(product => (
                        <div 
                            key={product.id}
                            style={{ 
                                border: '1px solid #ddd', 
                                padding: '15px', 
                                borderRadius: '8px',
                                cursor: 'pointer'
                            }}
                            onClick={() => addToCart(product)}
                        >
                            <h4>{product.name}</h4>
                            <p>{product.price} MAD</p>
                            <button>Ajouter au Panier</button>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

export default CaissePage;

// Fichier : src/renderer/src/App.jsx

import React, { useState, useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Importe les pages que tu vas créer
import ActivationPage from './pages/ActivationPage';
import LoginPage from './pages/LoginPage';
import IndexPage from './pages/IndexPage';
import CaissePage from './pages/CaissePage';

// Ce composant spécial protège les routes qui nécessitent d'être connecté.
function PrivateRoute({ children }) {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // On utilise l'API du preload pour savoir si un user est déjà connecté
        window.api.session.getCurrentUser().then(currentUser => {
            setUser(currentUser);
            setLoading(false);
        });
    }, []);

    if (loading) {
        return <div>Chargement de la session...</div>;
    }

    // Si pas d'utilisateur, on redirige vers la page de login
    return user ? children : <Navigate to="/login" />;
}

// Ce composant choisit la page à afficher après la connexion
function Dashboard() {
    const [user, setUser] = useState(null);
    useEffect(() => {
        window.api.session.getCurrentUser().then(setUser);
    }, []);

    if (!user) return <Navigate to="/login" />;

    // C'est ici qu'on refait la logique qui était dans main.js !
    if (user.role === 'Propriétaire') {
        return <IndexPage />;
    } else {
        return <CaissePage />;
    }
}

function App() {
  return (
    <Router>
        <Routes>
            <Route path="/activation" element={<ActivationPage />} />
            <Route path="/login" element={<LoginPage />} />

            {/* La route principale est protégée et redirige vers le bon dashboard */}
            <Route
                path="/"
                element={
                    <PrivateRoute>
                        <Dashboard />
                    </PrivateRoute>
                }
            />
            {/* Tu pourras ajouter d'autres routes ici, par exemple :
            <Route path="/produits" element={<PrivateRoute><ProduitsPage /></PrivateRoute>} />
            */}
        </Routes>
    </Router>
  );
}

export default App;

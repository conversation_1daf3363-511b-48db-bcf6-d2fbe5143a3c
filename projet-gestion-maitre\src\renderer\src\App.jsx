// Fichier : src/renderer/src/App.jsx

import React, { useState, useEffect } from 'react';
import { HashRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';

// Importe les pages
import ActivationPage from './pages/ActivationPage';
import LoginPage from './pages/LoginPage';
import IndexPage from './pages/IndexPage';
import CaissePage from './pages/CaissePage';
import MainLayout from './components/MainLayout'; // <-- NOUVEL IMPORT

// Un composant qui gère le chargement initial de l'application
function InitialLoader({ children }) {
    const [status, setStatus] = useState('loading');
    const location = useLocation();

    useEffect(() => {
        // Au démarrage, on vérifie si la licence est activée
        window.api.app.getInitialRoute().then(({ route }) => {
            if (location.pathname !== route) {
                setStatus(route); // Redirige vers la bonne route initiale
            } else {
                setStatus('ready'); // Déjà sur la bonne page
            }
        });
    }, []);

    if (status === 'loading') {
        return <div>Vérification de la licence...</div>;
    }

    if (status.startsWith('/')) {
        return <Navigate to={status} />;
    }

    return children;
}


// Ce composant spécial protège les routes qui nécessitent d'être connecté.
function PrivateRoute({ children }) {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        window.api.session.getCurrentUser().then(currentUser => {
            setUser(currentUser);
            setLoading(false);
        });
    }, []);

    if (loading) {
        return <div>Chargement de la session...</div>;
    }

    return user ? children : <Navigate to="/login" />;
}

// Ce composant choisit la page à afficher après la connexion
function Dashboard() {
    const [user, setUser] = useState(null);
    useEffect(() => {
        window.api.session.getCurrentUser().then(setUser);
    }, []);

    if (!user) return <Navigate to="/login" />;

    // C'est ici qu'on refait la logique qui était dans main.js !
    if (user.role === 'Propriétaire') {
        return <IndexPage />;
    } else {
        return <CaissePage />;
    }
}

function App() {
  return (
    <Router>
        <InitialLoader>
            <Routes>
                <Route path="/activation" element={<ActivationPage />} />
                <Route path="/login" element={<LoginPage />} />

                {/* On enveloppe notre Dashboard dans le MainLayout */}
                <Route
                    path="/*" // On capture toutes les autres routes ici
                    element={
                        <PrivateRoute>
                            <MainLayout>
                                <Routes>
                                    <Route path="/" element={<Dashboard />} />
                                    {/* On préparera les autres routes ici */}
                                </Routes>
                            </MainLayout>
                        </PrivateRoute>
                    }
                />
            </Routes>
        </InitialLoader>
    </Router>
  );
}

export default App;
